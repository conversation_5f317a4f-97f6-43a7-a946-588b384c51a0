<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM</title>

    <style>
        body{
            min-height: 3000px;
            background-image: linear-gradient(to bottom, #CCC, #333);
        }

        .info{
            max-width: 200px;
            position: sticky;
            top: 0;
            background-color: rgb(115, 180, 89);
            padding: 10px;
            box-shadow: 6px 6px 6px #111;
        }


    </style>


</head>
<body onresize="getViewport()" onscroll="getPageScroll()">

    <div class="info">
        <h1>Browser Information</h1>

        <p><b>Your Browser: </b></p>
        <p id="browser-agent"></p>
    
        
        <p><b>Curernt Width of Browser Viewport: </b></p>
        <p id="browser-width"></p>
    
        
        <p><b>Curernt Height of Browser Viewport: </b></p>
        <p id="browser-height"></p>
    
        <p><b>How Far You've Scrolled This Page (Pixels): </b></p>
        <p id="browser-scrollY"></p>
    </div>


    
    <script src="4_7 DOM.js"></script>
</body>
</html>