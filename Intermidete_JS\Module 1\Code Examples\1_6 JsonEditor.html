<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Json Editor</title>
</head>
<body>
    <h1>Simple JSON Editor</h1>
    <p>Write text into the textbox below, using proper JSON format</p>
    <p>Your valid JSON data will be converted into a real JS Object.</p>
    <div>
        <textarea name="jsonEditor" id="jsonEditor" cols="30" rows="10"></textarea>
    </div>
    <button onclick="createObjectFromJSON(jsonEditor.value)">Create Object From Json</button>

    <script src="1_6 JsonEditor.js"></script>
</body>
</html>