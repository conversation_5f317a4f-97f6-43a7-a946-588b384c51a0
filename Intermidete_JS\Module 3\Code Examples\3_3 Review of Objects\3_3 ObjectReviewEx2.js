'use strict';

function SimpleObject(name){
    this.name = name;
    this.method = function() {console.log(this.name)};
}

let obj1 = new SimpleObject();
//What JS DataType is it?
console.log(typeof(obj1) ); //Object 

//What is the name of the constructor that generated this object? 
console.log(obj1.constructor.name); //SimpleObject 

//is it an instance of the SimpleObject Constructor/Type?
console.log(obj1 instanceof SimpleObject) //true,

//DataType is Object
//What SubType / Object Type 
//Constructor
