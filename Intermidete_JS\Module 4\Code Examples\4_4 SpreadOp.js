'use strict'
//Rest and Spread Operator

//a simple function with 2 arguements/inputs
function add(n1, n2){
    return n1 + n2;
}
console.log(add(1) );  //NAN, because the 2nd arguement is missing.
console.log(add(1, 2) );  //3
console.log(add(1, 2, 3) ); //3 , because the thrid arguement is ignored.


//The Rest Operator. Allows for an unlimited number of arguements. Used like an array
//The ...Rest must be the LAST arguement listed.
function addAllNumbers(...numbers){
    let sum = 0;
    for (let n of numbers){
      sum += n;
    }
    return sum;
}
console.log(addAllNumbers(1) );  //1
console.log(addAllNumbers(1, 2) );  //3
console.log(addAllNumbers(1, 2, 3) ); //6 , 
console.log(addAllNumbers(1, 2, 3, 4) ); //10 , 

//Example 2
function multipleThenAdd(n1, n2, ...args){
    let result = n1 * n2;
    for(let n of args){
        result += n;
    }
    return result;
}

console.log(multipleThenAdd(3,2) ) //6
console.log(multipleThenAdd(3,2,1) ) //7


//The Spread Operator
let someArray = [5,9,13,4,6,2,11,5]
let max = Math.max(someArray);
console.log(max) //Returns NaN

max = Math.max(...someArray);
console.log(max) //returns 13, the largset number in the array 