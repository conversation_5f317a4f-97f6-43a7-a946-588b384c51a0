body, html{
    margin:0;
}

nav{
    background-color: rgb(43, 78, 112);
    color: white;
    padding: 1rem;
    margin: 1rem;
}

nav header{
    text-align: center;
    display: inline-block;
    font-size: 2em;
}
nav ul{
    display: inline-block;
    font-size: 1.4em;
}

nav ul li{
    display: inline-block;
    margin: 10px;
}

.flex-container{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin: 1rem;
}

/* Form Style */
#bookformwindow, #patronformwindow, #checkoutformwindow{
    padding: 1em;
    min-height: 100%;
    flex-basis: 25%;
    flex-shrink: 1;
    background-color: rgb(20, 35, 49);
}

label{
    display:block;
    color:white;
    font-size: 1.1em;
    margin: 10px;
}

input, select{
    display: block;
    padding: .5rem;
    width: 90%;
    margin: 10px;
}

/* Button Styles */
.btn{
    padding: 12px;
    font-size: 1rem;
    margin: 1em;
    background-color: rgb(22, 88, 150);
    border: none;
    color: white;
}

.reset{
    background-color: rgb(22, 150, 80);
}

/* Show Hide Toggle */
.hide{
    display: none;
}

.show {
    display: initial;
}

/*Style for Book and Patrons */
.record{
    display:grid;
    grid-template-columns: repeat(6, 1fr);
    justify-content:  space-evenly;
    padding: .5em;
    gap: 10px;
}