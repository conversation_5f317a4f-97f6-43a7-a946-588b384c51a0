<!DOCTYPE html>
<html>
  <head>
    <style>
      body{
        font-size: 1.5em;
      }

      .info{
        background-color: #aaa;
        padding: 1em;
      }

      #flex-row{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      #flex-row div{
        padding: 10px;
        background-color: #6fa6dd;
        margin: 5px;
      }

    </style>
  </head>
  <body>
    <h1>JS Arrays</h1>

    <p class="info">The output shown below was created by JavaScript, and placed on this page. Open the Browser Inspector and goto the Console to view 
      more array examples printed to Console.
    </p>

    <p>Array elements loaded into a flex box and styled.</p>
    <div id="flex-row">

    </div>
    
    <script src="2_2 Arrays.js"></script>
  </body>
</html>