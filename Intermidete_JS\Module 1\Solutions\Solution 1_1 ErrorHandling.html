<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Handling Assignment</title>

    <style>
        .warning{
            color: red;
        }

        #table *
        {   border: 1px solid black;
            border-collapse: collapse;
            padding: .5em;
        }
    </style>

</head>
<body>
    <h1>Error Handling Assignment</h1>
    <p>Modify the 1_1 ErrorHandling.js by adding try/catch statements to catch the errors.</p>
    <p>The goal is to catch all errors so that the entire script runs. Do not correct the errors, simply catch them.</p>


    <ol id ="list">

    </ol>

    <table id="table">
        <th>Object Property</th>
        <th>Object Value</th>
    </table>
    
    <script src="Solution 1_1 ErrorHandling.js"></script>

</body>
</html>