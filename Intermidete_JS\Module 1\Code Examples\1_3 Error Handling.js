"use strict";

//throw "My Custom Error";

//Error Handling Example
let someNumber = 5;

try{
  someNumber.toUpperCase();
  //Will throw an error because someNumber is not a string. It has no toUpperCase() method.
  }
catch (e){
  //the input parameter (e) is the error message passed to the catch block.
  console.log("An error occured.  The error was : ")
  console.error(e.message); //We catch the error. We can log the details of the error.
  someNumber = "five";
  //We can resovle this issue by setting someNumber to Text.
}

//Because the error was handled, the code continues to run.
someNumber = someNumber.toUpperCase(); //FIVE
console.log(someNumber);
console.log("You've reached the end of the script.");

