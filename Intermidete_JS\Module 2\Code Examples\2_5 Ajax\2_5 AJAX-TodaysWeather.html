<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="html, training, lessons, templates">
  <meta lang="en-US">

  <title>Ajax</title>
  <style>
    code {
      width: '500px';
      height: '500px';

    }

    #btn1 {
      width: '75px';

    }

    #forecast {
      margin: 20px;
      padding: 5px;
      font-size: 24px;
      background-color: cadetblue;
      border: 2px solid black;
    }
  </style>


</head>


<body>
  <h1>Ajax - Get Weather From The National Weather Service </h1>
  <p>By clicking the button below, a JS AJAX request will contact weather.gov and get your local weather data. </p>
  <p>We'll also show the raw JSON data as well that contains all of the data that we pulled today's forecast from.
  </p>
  <p>
    Due to the way the National Weather Service's system works, we have to run three functions and and make 2 AJAX calls.
    <ol>
      <li>First, we have to use navigator.gerolocation to get our location coordinates</li>
      <li>And then, we make our first ajax call to the NWS to get the local weather station at our coordinates</li>
      <li>And then, we make a call to that weather station for today's forecast.</li>
    </ol>
  </p>

  <h1>Today's Forecast</h1>
  <div id="forecast">

  </div>



  <h2>Raw JSON Data:</h2>
  <code id='target'> </code>

  <div>
    <input type='button' id="btn1" onclick="getLocation(getWeatherStation, getForecast)" value='Request Data'>
  </div>

  <script>

    //First , we need to get our latitude and longitude, from navigator.geolocation.
    //When that information comes back, we callback to getWeatherStation, sending
      //1. our latitude
      //2. our longitude
      //3. a callback for getForecast.
    function getLocation(getWeatherStation, getForecast){
      navigator.geolocation.getCurrentPosition( (location) => {
        getWeatherStation(location.coords.latitude, location.coords.longitude, getForecast);
      } );
    }


    //Second, we ask for our local weather station.
    function getWeatherStation(latt, long, getForecast) {
      var xhttp = new XMLHttpRequest();

      //What we want to have happen when we get the a response back from weather.gov
      //Remember, this code has NOT ran yet!
      xhttp.onreadystatechange = function () {
        if (this.readyState == 4 && this.status == 200) {
          document.getElementById("target").innerHTML = this.responseText;
          var stationInformation = JSON.parse(this.responseText);
          //Now that our weather station is known, callback getForeCast, providing it with the weather station information it needs.
          getForecast(stationInformation.properties.forecast)
        }
      };

      //Send our request to get our local weather station, providing our latitude and longitude
      xhttp.open("GET", `https://api.weather.gov/points/${latt},${long}`);
      xhttp.send();
      //Now we wait for the response. The OnReadyState callback will fire when the the reponse comes back.

    }

  //Once we have have our local weather station url, we can ask it for today's forecast.
  function getForecast(url){
    var xhttp = new XMLHttpRequest();
    //Remember this code has not run yet. We need to wait for the reponse from the weather station.
    xhttp.onreadystatechange = function () {
        if (this.readyState == 4 && this.status == 200) {
          document.getElementById("target").innerHTML = this.responseText;
          //display raw JSON data

          //Get the forecast details for today, and make them pretty in the HTML element.
          var forecast = JSON.parse(this.responseText);
          document.getElementById("forecast").innerHTML = forecast.properties.periods[1].detailedForecast;
        }
      };

      xhttp.open("GET", url);
      xhttp.send();
      //Send the 2nd request and wait for response.
  }

  </script>


</body>

</html>