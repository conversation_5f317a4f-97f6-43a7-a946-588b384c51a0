'use strict';
//Set Interval

//Get a reference to the HTML element with id Clock  
const clockHTML = document.getElementById("clock")
let clockRunning = false;
let intervalId = null;  //setInterval returns an interval ID for each instance of the function. We'll need to capture this to implement
//the stop clock feature.

//For formatting purposes only.  If the time unit is a single digit 0-9, format it as 01, 02, 03, etc.
function formatTime(t){
    if (t < 10){
        t = `0${t}`;
    }
    return t;
}


//Stop the clock.  clearInterval needs the  Id provided to us when we called setInterval()
function stopClock(){
    clearInterval(intervalId);
    clockRunning = false;
}

function startClock(){
    
    
    //if the clock is already running, we don't want to run setInterval again, so just return.
    if(clockRunning){
        return
    }

    //Call setInterval.
    //We will run this code on a set interval of every 1 second (1000 ms);
    //Get the current Date. Extract and format the hours, minutes, and seconds. 
    //Display to HTML.
    //We also need to store the interval ID value returned so we can stop it later with clearInterval.
    intervalId = setInterval( ()  =>{
        let clock = new Date();
        let hours = clock.getHours() -12
        let minutes = formatTime(clock.getMinutes());
        let seconds = formatTime(clock.getSeconds());
        clockHTML.innerText = ` ${hours}:${minutes}:${seconds}`
    }, 1000); //runs every 1000 milliseconds (1 second)
    
    clockRunning = true;

}

//When the script loads, start the clock.
startClock();