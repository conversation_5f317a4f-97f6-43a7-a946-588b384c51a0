<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="html, training, lessons, templates">
  <meta lang="en-US">

  <title>JS Break And Continue</title>
  <style>
    #target {
      box-sizing: border-box;
      min-width: 500px;
      min-height: 500px;
      background-color: rgb(200, 200, 200);
      border: 2px solid black;
      font-size: 2em;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 20px;
    }


    #btn1 {
      width: '75px';
      margin: auto;
      float: right;
      padding: 10px;
    }

    .flex-row {
      display: flex;
      flex-direction: row;
    }

    #options{
      display: flex;
      flex-direction: column;
      background-color: rgb(60, 60, 60);
      color: white;
      padding: 10px;
      font-size: 1.4em;
      max-width: 25%;

    }

    #options *{
      margin: 10px;
    }

    #options input{
      padding: 5px;
      min-height: 25px;
    }

    .about{
      padding: 20px;
      justify-self: right;
      max-width: 200px;
      background-color: khaki;
      margin: 0 20px;
      font-size: 1.1em;
      line-height: 1.5em;

    }


      

    
  </style>


</head>


<body>
  <h1>JS Break and Continue</h1>


  <div class="flex-row">
    <div id="options">
      <label for='breakon'>Enter a number to break the loop on. The loop will exit on this break.</label>
      <input type=number id='breakon' name='inputbox1' value="" min="1" max="20">
      <label for='continue-on'>Enter a number to skip on a loop. Other loop runs will continue as normal </label>
      <input type=number id='continue-on' name='inputbox2' value="" min="1" max="20">

      <button type='button' id="btn1" onclick="runLoop()">Run Loop</button> 
     </div>


    <div id='target'>
      <div id='output'>

      </div>
    </div>

    <div class="about" >
      <p>
        <b>Break </b>  will force a function or loop to exit early. Any code remaining after the break will not be executed.
      </p>

      <p>
        <b>Continue</b> will stop a single run of a loop. The loop will continue on at the start the next run of the loop.
      </p>
    </div>

  </div>




  <script src="3_3 BreakContinue.js">
  </script>

</body>

</html>