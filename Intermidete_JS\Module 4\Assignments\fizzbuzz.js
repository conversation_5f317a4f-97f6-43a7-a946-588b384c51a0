'use strict';

/**
 * FizzBuzz function that returns the appropriate string for a given number
 * @param {number} num - The number to evaluate
 * @returns {string} - Returns "FizzBuzz", "Fizz", "Buzz", or the number as string
 */
function fizzBuzz(num) {
    if (num % 3 === 0 && num % 5 === 0) {
        return "FizzBuzz";
    }
    else if (num % 3 === 0) {
        return "Fizz";
    }
    else if (num % 5 === 0) {
        return "Buzz";
    }
    else {
        return num.toString();
    }
}

/**
 * Runs FizzBuzz for numbers 1 to 100 and prints results
 */
function runFizzBuzz() {
    for (let i = 1; i <= 100; i++) {
        console.log(fizzBuzz(i));
    }
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { fizzBuzz, runFizzBuzz };
}

// Run the original logic if this file is executed directly
if (require.main === module) {
    runFizzBuzz();
}
