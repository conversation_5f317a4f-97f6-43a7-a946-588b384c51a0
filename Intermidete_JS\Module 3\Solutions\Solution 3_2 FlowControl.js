"use strict"
//User Input 
let name = prompt("Please enter your name");
let userReview = prompt("Please Leave a Movie Rating between 1 and 4 Stars.");
let userColor = prompt("Please choose a color from the following: red, blue, yellow , green, purple.");
let hungry = confirm("Are you hungry? Click OK for YES or Cancel For NO");
let lunch; //declare lunch, but don't assign it a value

//if hungry is true, set lunch equal to prompt input.
if(hungry){
    lunch = prompt("Would you like pizza, burger, or a salad for lunch?")
}

//Movie Review
let review = document.createElement("p");
review.className = "review";
document.getElementById("movie-review").append(review);

if(userReview === '4')
{
    review.innerText = `${name} left a 4 star review. They thought the movie was the best.`;
}

else if(userReview === '3')
{
    review.innerText = `${name} left a 3 star review. They thought the movie was good.`;
}

else if (userReview === '2')
{
    review.innerText = `${name} left a 2 star review. They though the movie was ok.`;
}

else if (userReview === '1')
{
    review.innerText = `${name} left a 1 star review. They didn't like the movie at all`;
}

else{
    review.innerText = "Sorry, we aren't sure what kind of review you left ";
}


//Color Cube Change
function colorCube(color)
{
    let cube = document.getElementById("cube");
    cube.style.backgroundColor = color;
}

//remember to include break after each case!
userColor = userColor.toLowerCase();
switch(userColor){
    case 'red':
        colorCube(userColor);
        break;
    case 'blue':
        colorCube(userColor);
        break;
    case 'yellow':
        colorCube(userColor);
        break;
    case 'purple':
        colorCube(userColor);
        break;
    case 'green':
        colorCube(userColor);
        break;
    default:
        colorCube("orange");
                                              
}


//Lunch Order
function orderLunch(lunch)
{

    let lunchOrder = document.createElement("p");
    lunchOrder.innerText = `${name} ordered a ${lunch} for lunch`;
    document.getElementById("lunch").append(lunchOrder);
}


function lunchNotAvailable(){
    let lunchOrder = document.createElement("p");
    lunchOrder.innerText = `Sorry, what you wanted wasn't available.`;
    document.getElementById("lunch").append(lunchOrder);

}

function notHungry(){
    let lunchOrder = document.createElement("p");
    lunchOrder.innerText = `Seems like you are not hungry`;
    document.getElementById("lunch").append(lunchOrder);

}

//If they are hungry, switch the lunch order and orderLunch.
if (hungry)
{
    switch(lunch){
        case 'pizza':
            orderLunch(lunch);
            break;
        case 'burger':
            orderLunch(lunch);
            break;
        case 'salad':
            orderLunch(lunch);
            break;
        default:
            lunchNotAvailable()
            //if what they wanted was not available
    }
}
else{
    notHungry();
}
