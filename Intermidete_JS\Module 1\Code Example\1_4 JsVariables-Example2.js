'use strict';
//Say Hello World.
console.log("Hello World");

//create the variable x. assign it the value of 5.
let x = 5;

//create the variable y. assign it the value of 5.
let y = 5;

//create the variable myFavoriteMovie and assign it a value
var myFavoriteMovie = 'The Great Escape'; 
console.log("My favorite movie is " + myFavoriteMovie);

//is x is equal to the value of y, print to the screen.
console.log("Is variable x equal to variable y?")
console.log(x === y);  


//Set y equal to it's current value plus 3
//print it's value to screen.
//We've already created y so we won't use keyword var or let here a second time.
y += 3;
console.log("The new vale of y is")
console.log(y);

//create the variable called result, and assign it the value of x minus y.
//print the value of result to the console
let result = x - y;
console.log("The result of x - y is")
console.log(result);


//Create variable called two, assign it the value 2
//Create variable one, assign it the value of 1
//Create variable true_or_false, assign the the value of comparing is 2 greater than  1?
let two = 2;
let one = 1;
let true_or_false = two > one;
console.log("Is two greater than one?")
console.log(true_or_false); 

//Assign true_or_false the value of comparing is two less than 1?
//we've already created true_or_false, so we won't use keyword var again here
true_or_false = two < one;
console.log("Is two less than than one?")
console.log(true_or_false);
//returns false

