//CALL STACK BAD Example.
//This function calls itself over and over.
//It never can complete, because it's always waiting for the next copy of itself to finsh.
//Eventually an error will be thrown that the max number of calls on the call stack have been reached
//the script will stop.

//We are lucky. If the JS environment didn't stop the runaway calls, the entire computer would crash, eventually.

function badFunction(){
    console.log("Starting Bad Function");
    badFunction();
    console.log("Bad Function Complete.") // We never reach this line of code.
}

badFunction();