body {
    background: rgb(51, 51, 51);
    height: 100%;
    margin: 0px;
    font-family: Arial, Helvetica, sans-serif;
}
header {
    text-align: center;
    color: white;
}
#stories-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
#stories-container article {
    display: inline-block;
    width: 16rem;
    margin: 1rem;
    padding: 1.5rem;
    background-color: rgb(250, 250, 250);
    border: lightgray 0.1rem solid;
    border-radius: 0.5rem;
    box-shadow: 0.1rem 0.05rem 0.05rem lightgray;
}
#stories-container article h3 {
    margin-top: 0px;
}
#stories-container article img {
    width: 16rem;
    margin-bottom: .5rem;
}
.no-display {
    display: none !important;
}
#refresh-btn {
    display: block;
    margin: 1rem auto;
    font-weight: 400;
    color: #212529;
    text-align: center;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    color: #fff;
    background-color: #5cb85c;
}
.showStory {
    display: block;
    margin: 1rem auto;
    font-weight: 400;
    color: #212529;
    text-align: center;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    color: #fff;
    background-color: #007bff;
}

#return {
    display: block;
    margin: 1rem auto;
    font-weight: 400;
    color: #212529;
    text-align: center;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    color: #fff;
    background-color: #007bff;
}

