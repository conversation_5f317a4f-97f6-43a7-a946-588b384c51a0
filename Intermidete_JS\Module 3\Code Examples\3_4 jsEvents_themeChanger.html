<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="html, training, lessons, templates">
    <meta lang="en-US">
    
    <title>JS Events Theme Changer</title>
    <style>

      body, html{
        margin: 0px;
      }

      main{
        height: 100vh;
        margin: 0;
        display: flex;
        flex-direction: column;
      }

      .flex-row{
        display: flex;
        flex-direction: row;
        flex-grow: 1;
      }

      .flex-column{
        display: flex;
        flex-direction: column;
        min-width: 15vw;
      }

      .page-content{
        padding: 1em 2em;
        font-size: 1.5em;
      }

      .page-content h1{
        text-align: center;
      }

      header{
        padding: 1.2em;
      }

      label{
        text-align: center;
        font-size: 1.3em;
      }

      select{
        font-size: 1.3em;
        border-radius: 5px;
        padding: 20px;
        margin: 20px;
      }


      .light-header{
        background-color: #CCC;
      }

      .light-sidebar{
        background-color: #CCC;
      }

      .light-content{
        border: 6px solid black;
        background-color: #EEE;

      }

      .dark-header{
        background-color: rgb(59, 59, 59);
        color: white;
      }

      .dark-sidebar{
        background-color: rgb(59, 59, 59);
        color: white;

      }

      .dark-content{
        border: 6px solid black;
        background-color: rgb(102, 102, 102);
        color: white;
      }

      .blue-header{
        background-color: rgb(22, 103, 141);
        color: white;
      }

      .blue-sidebar{
        background-color: rgb(22, 103, 141);
        color: white;

      }

      .blue-content{
        border: 6px solid rgb(250, 250, 250);
        background-color: rgb(76, 133, 160);
        color: white;

      }

      .green-header{
        background-color: rgb(49, 100, 65);
        color: white;
      }

      .green-sidebar{
        background-color: rgb(49, 100, 65);
        color: white;

      }

      .green-content{
        border: 6px solid rgb(250, 250, 250);
        background-color: rgb(31, 65, 41);
        color: white;

      }

    </style>


  </head>


  <body>
    <main>
      <header id="header" class="light-header">
        <h1>JS Events Theme Changer</h1>
      </header>
      <section class="flex-row">
        <div id="sidebar" class="flex-column light-sidebar">
          <label for="themes">Select A Theme</label>
          <select name="themes" id="themes" onchange="refreshTheme(this)">
            <option value="Light">Light Theme</option>
            <option value="Dark">Dark Theme</option>
            <option value="Blue">Blue Theme</option>
            <option value="Green">Green Theme</option>
          </select>
        </div>
        <div id="content" class="page-content light-content">
          <h1>Welcome To My Web Site!</h1>
          <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Temporibus culpa cupiditate consectetur. Voluptate maxime blanditiis ipsum! Non nostrum saepe placeat omnis. Sapiente, beatae reprehenderit voluptatem minima repellat ipsum placeat laborum.</p>
          <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Modi consequatur suscipit officia repellat cumque officiis mollitia nulla similique libero perferendis.</p>
          <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Blanditiis quam deleniti odit recusandae officia iste consequuntur ipsum voluptatibus ipsa praesentium. Accusamus modi iusto unde in voluptatem mollitia dolorum nostrum veniam obcaecati reprehenderit recusandae, soluta provident.</p>
        </div>

      </section>
     </main>

     <script>
       function refreshTheme(selectControl){
        let sidebar = document.getElementById("sidebar");
        let content = document.getElementById("content");
        let header = document.getElementById("header");
 
        
        switch(selectControl.value)
         {
          case 'Light': {
            header.className = "light-header"
            sidebar.className = "flex-column light-sidebar"
            content.className = "page-content light-content"
            break;
           }

           case 'Dark': {
            header.className = "dark-header"
            sidebar.className = "flex-column dark-sidebar"
            content.className = "page-content dark-content"
            break;
           }

           case 'Blue': {
            header.className = "blue-header"
            sidebar.className = "flex-column blue-sidebar"
            content.className = "page-content blue-content"
            break;
           }

           
           case 'Green': {
            header.className = "green-header"
            sidebar.className = "flex-column green-sidebar"
            content.className = "page-content green-content"
            break;
           }

         }
       }
     </script>

  </body>
</html>