//get the canvas and set the context to 2D mode.
let canvas = document.getElementById('canvas');
let context = canvas.getContext('2d');

//This example only works correctly on a full size canvas.
canvas.width = window.innerWidth;  
canvas.height = window.innerHeight; 

let dragging = false;
let penSize = 5;


context.lineWidth = penSize * 2; 

let OnDraw = function(eventData){
	if(dragging){
		context.lineTo(eventData.pageX, eventData.pageY);
	  context.stroke();
	}
}

let BeginDraw = function(eventData){
  dragging = true;
  console.log(eventData);
}

let EndDraw = function(){
	dragging = false;
	context.beginPath();
}

