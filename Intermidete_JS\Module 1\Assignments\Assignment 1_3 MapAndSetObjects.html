<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map and Set Assignment</title>

    <style>
        .flexbox{
            display: flex;
        }

        .background-dark{
            background-color: #333;
            color: rgb(218, 218, 218);
            padding: 1rem;
        }
        .background-light{
            background-image: linear-gradient(45deg,rgb(185, 238, 252),rgb(52, 165, 231));
            padding: 1rem;
        }

        #stats{
            display: flex;
            flex-direction: column;
        }

        #stats div {
            text-align: right;
            padding: .5rem;;
        }

        .btn{
            background-color: darkred;
            color: white;
            border: none;
            padding: .5em;
            margin: .2em;
        }

        .btn-green{
            background-color: rgb(0, 139, 35);
            color: white;
            border: none;
            padding: .5em;
            margin: .2em;
        }

        .flex-col{
            flex-grow: 1;
        }


        .country-heading{
            margin-top: 0;
            
        }

        .country{
            padding: 1em;
            background-color: papayawhip;
            margin: .5em;   
            border: 1px solid black;
        }
    </style>

</head>

<body>


    <h1>Map And Set Assigmnet</h1>
    
    <section>
    <h2>Working With Map</h2>
    <p>In the HTML below, you will create "stats" for your game character and store them in a Map object.  Then, using the Map
        object, you'll display the character information back to the page in the "character window."
    </p>
    <div class="background-dark">
        <h3>JavaScript Souls "Prepare to Code" Character Creation</h3>
        <div class="flexbox">
            <div id="stats">
                <div>
                    <label for="name">Name</label>
                    <input type="text" id="characterName" onchange="updateStats()">
                </div>
                <div>
                    <label for="vitality">Vitality</label>
                    <input type="number" id="vitality" min=1 onchange="updateStats()">
                </div>
                <div>
                    <label for="strength">Strength</label>
                    <input type="number" id="strength" min=1 onchange="updateStats()">
        
                </div>
                <div>
                    <label for="dex">Dexterity</label>
                    <input type="number" id="dex" min=1 onchange="updateStats()">
        
                </div>
                <div>
                    <label for="intelligence">Intellgence</label>
                    <input type="number" id="intelligence" min=1 onchange="updateStats()">
                </div>
            </div>
            <div id="characterWindow">
                <div id="character">
                    <p></p>
                    <p></p>
                    <p></p>
                    <p></p>
                    <p></p>
                </div>
            </div>
        </div>
        <input type="button" class="btn" value="Display Character" onclick="displayCharacter()">

    </div>
    </section>

    <section>
    <h2>Working With Set</h2>
    <p>In the HTML below, you will select from a possible list of countries. The countries will be added to a Set object. Then, using the Set
        object, you'll display the list of countries visited back to the page in the "country list." 
    </p>
    <div class="background-light">
        <h3>World Traveler Tracker</h3>
        <div class="flexbox space-around">
            <div class="flex-col">
                <label for="countrySelect">Select A Country</label>
                <select name="countrySelect" id="countrySelect">
                    <option value="China">China</option>
                    <option value="Japan">Japan</option>
                    <option value="Germany">Germany</option>
                    <option value="India">India</option>
                    <option value="France">France</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Brazil">Brazil</option>
                    <option value="Italty">Italty</option>
                    <option value="Canada">Canada</option>
                    <option value="Egypt">Egypt</option>
                    <option value="Russia">Russia</option>
                    <option value="Mexico">Mexico</option>
                    <option value="South Africa">South Africa</option>
                    <option value="Australia">Australia</option>
                </select>
                <div>
                    <input type="button" class="btn-green" value="Add Country" onclick="addCountry()">
                </div>
            </div>
            <div class="flex-col">
                <h4 class="country-heading">Countries Visited</h4>
                <div id="country-list">

                </div>
            </div>
        </div>
    </div>

    </section>


    <script src="Solution 1_3 MapAndSetObjects.js"></script>
    
</body>
</html>