
//for the ondragstart event.....
function beginDrag(event){
  console.log(event);
  event.dataTransfer.setData("text/html", event.target.id);
}

function onDragOver(event)
{
  event.preventDefault();  //wait a minute <PERSON>rowser, I'm going to deal with this myself.
  event.dataTransfer.dropEffect="move";
}

function drop(event)
{
  event.preventDefault(); //wait a minute Browser, I'm going to deal with this myself.
  let data = event.dataTransfer.getData("text/html",);
  event.target.append(document.getElementById(data));
}