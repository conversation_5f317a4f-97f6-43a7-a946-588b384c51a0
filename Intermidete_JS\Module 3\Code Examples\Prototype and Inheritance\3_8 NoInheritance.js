'use strict';
//Example of No Inheritance
function Animal(name, arms = 2, legs = 2, dangerous = false, furry = false){
    this.name = name;
    this.arms = arms;
    this.legs = legs;
    this.isDangerous = dangerous;
    this.isFurry = furry;
    this.eats = function(){return this.name + " eats some food." };
    this.sleep = function() {return this.name + " goes to sleep" }; 
}


let bobo = new Animal("Bobo the Monkey", 2, 2, false, true);
let friendly = new Animal("Friendly the Spider", 0, 8, false, false)
let crazyCat = new Animal("Crazy Cat", 0, 4, true, true);

console.log(bobo);
console.log(friendly);
console.log(crazyCat);
console.log ( bobo.eats() );


function Dog(name, dangerous = false){
    this.name = name;
    this.arms = 0;
    this.legs = 4;
    this.isDangerous = dangerous;
    this.isFurry = true;
    this.eats = function(){return this.name + " eats some food." };
    this.sleep = function() {return this.name + " goes to sleep" }; 
    this.bark = function(){return this.name + "goes BARK BARK!"};
}

//The issue here is that we repeat ourselves with the Dog class. We want a way to borrow from the Animal class
//We also want the answer to the question is a Dog an Animal to be true. Current a dog is not an animal.

let myDog = new Dog("Lucky");
console.log (myDog instanceof Animal) //Is a Dog an Animal?  This code says no.