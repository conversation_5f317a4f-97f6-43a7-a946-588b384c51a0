class BankCustomer{
    
    _bankAccount;

    constructor(firstName, lastName, age)
    {
        this.firstName = firstName;
        this.lastName = lastName;
        this.age = age;
    }

    get bankAccount(){
        return this._bankAccount;
    }

    set bankAccount(value){
        if(value < 0){
            if(this._bankAccount > value * -1)
            this._bankAccount -= value * -1;
            else{
                console.log("Not enough money in Bank Account");
            }
        }

        else{
           this._bankAccount += value;
        }
    }
}
    
