<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creating Objects</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comic+Neue:ital,wght@1,300&display=swap" rel="stylesheet">

    <style>

        body{
           font-family: 'Comic Neue', cursive;
        }
        .flex-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .flex-row-wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .flex-col {
            display: flex;
            flex-direction: column;
        }

        header{
            text-align: center;
            padding: 20px;
            font-size: 2.5em;
        }

        input, select, button{
            width: 100%;
            margin: 5px 0;
            padding: 5px;
        }

        .hero-card {
            border: 6px solid rgb(255, 255, 0);
            background-image: linear-gradient(45deg, rgb(0, 197, 102), rgb(93, 190, 255));
            color: rgb(255, 255, 162);
            font-size: 1.3em;
            outline: 4px solid black;
            padding: 2em;
            margin: 2em;    
            min-width: 300px;    }
    </style>

</head>

<body>
    <header>
        Super Hero Creator
    </header>
    <div id="controls" class="flex-row-wrap">
        <input type="text" id="heroname" placeholder="Hero Name">
        <input type="text" id="realname" placeholder="Real Name">
        <input type="text" id="home" placeholder="Home World">
        <select name="superpowers" id="superpowers">
            <option value="Flying">Flying</option>
            <option value="X-ray vision">X-ray vision</option>
            <option value="Super Strength">Super Strength</option>
            <option value="Super Intelligence"> Super Intelligence</option>
            <option value="Control Minds">Control Minds</option>
            <option value="Create Double">Create Double</option>
        </select>
        <select name="weakness" id="weakness">
            <option value="Water">Water</option>
            <option value="Pickles">Pickles</option>
            <option value="Talks In Binary Code">Talks In Binary Code</option>
            <option value="None">none</option>
        </select>
        <button onclick="createHero()"> Create Hero</button>

    </div>
    <div id="hero-viewer" class="flex-row">

    </div>
    <script src="Assignment 4_1.js"></script>
</body>

</html>