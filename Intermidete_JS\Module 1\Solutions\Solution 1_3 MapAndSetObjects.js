"use strict";

//Map Exercise
//global variables for referencing html elements needed.
const character = new Map();
const characterName = document.getElementById("characterName");
const vitality = document.getElementById("vitality");
const strength = document.getElementById("strength");
const dexterity = document.getElementById("dex");
const intelligence = document.getElementById("intelligence");
const characterHTML = document.getElementById("character")

//This function should update the map object by saving the values from the html input elements into the map object.
function updateStats(){
    character.set("characterName", characterName.value);
    character.set("vitality", vitality.value);
    character.set("strength", strength.value);
    character.set("dexterity", dexterity.value);
    character.set("intelligence", intelligence.value);
}

//For each item stored in the map object, we should display both the map "key", the map "value" into the character HTML element.
function displayCharacter(){
    let i = 0;
    for(let stat of character)
    {

        characterHTML.children[i].innerText = `${stat[0]} : ${character.get(stat[0])}`
        i++    
    }
}

//Set Exercise
//A list of global variables needed to reference elements on the page
const visited = new Set();
const selectBox = document.getElementById("countrySelect")
const countryList = document.getElementById("country-list")


//This function should add the country from the select list into the set object,
 //then clear the HTML of the country list
 //then, for each item in the set object, display it to the page.
function addCountry(){
    visited.add(selectBox.value);
    countryList.innerHTML = "";
    for(let country of visited)
    {
        let html = document.createElement("p");
        html.className = "country"
        html.textContent = country;
        countryList.append(html);

    }

}