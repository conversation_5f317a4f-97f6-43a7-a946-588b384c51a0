'use strict';

/**
 * Find duplicates in an array using recursion
 * @param {Array} arr - The input array
 * @param {number} index - Current index (for recursion)
 * @param {Set} seen - Set to track seen elements
 * @param {Set} duplicates - Set to store duplicates
 * @returns {Array} - Array of duplicate elements
 */
function findDuplicatesRecursive(arr, index = 0, seen = new Set(), duplicates = new Set()) {
    // Base case: if we've processed all elements
    if (index >= arr.length) {
        return Array.from(duplicates);
    }
    
    const currentElement = arr[index];
    
    // If we've seen this element before, it's a duplicate
    if (seen.has(currentElement)) {
        duplicates.add(currentElement);
    } else {
        // Mark this element as seen
        seen.add(currentElement);
    }
    
    // Recursive call for the next element
    return findDuplicatesRecursive(arr, index + 1, seen, duplicates);
}

/**
 * Alternative recursive approach using array slicing
 * @param {Array} arr - The input array
 * @param {Set} seen - Set to track seen elements
 * @param {Set} duplicates - Set to store duplicates
 * @returns {Array} - Array of duplicate elements
 */
function findDuplicatesRecursiveSlice(arr, seen = new Set(), duplicates = new Set()) {
    // Base case: empty array
    if (arr.length === 0) {
        return Array.from(duplicates);
    }
    
    const [first, ...rest] = arr;
    
    // If we've seen this element before, it's a duplicate
    if (seen.has(first)) {
        duplicates.add(first);
    } else {
        seen.add(first);
    }
    
    // Recursive call with the rest of the array
    return findDuplicatesRecursiveSlice(rest, seen, duplicates);
}

/**
 * Recursive approach that finds all occurrences of duplicates
 * @param {Array} arr - The input array
 * @param {number} index - Current index
 * @param {Object} counts - Object to track element counts
 * @returns {Array} - Array of elements that appear more than once
 */
function findDuplicatesWithCounts(arr, index = 0, counts = {}) {
    // Base case: processed all elements
    if (index >= arr.length) {
        // Return elements that appear more than once
        return Object.keys(counts).filter(key => counts[key] > 1);
    }
    
    const currentElement = arr[index];
    
    // Count occurrences
    counts[currentElement] = (counts[currentElement] || 0) + 1;
    
    // Recursive call
    return findDuplicatesWithCounts(arr, index + 1, counts);
}

/**
 * Helper function to find the first duplicate using recursion
 * @param {Array} arr - The input array
 * @param {number} index - Current index
 * @param {Set} seen - Set of seen elements
 * @returns {*} - First duplicate found, or null if none
 */
function findFirstDuplicate(arr, index = 0, seen = new Set()) {
    // Base case: no more elements
    if (index >= arr.length) {
        return null;
    }
    
    const currentElement = arr[index];
    
    // If we've seen this element, it's our first duplicate
    if (seen.has(currentElement)) {
        return currentElement;
    }
    
    seen.add(currentElement);
    
    // Recursive call
    return findFirstDuplicate(arr, index + 1, seen);
}

/**
 * Check if array has duplicates using recursion
 * @param {Array} arr - The input array
 * @param {number} index - Current index
 * @param {Set} seen - Set of seen elements
 * @returns {boolean} - True if duplicates exist, false otherwise
 */
function hasDuplicates(arr, index = 0, seen = new Set()) {
    // Base case: no more elements, no duplicates found
    if (index >= arr.length) {
        return false;
    }
    
    const currentElement = arr[index];
    
    // If we've seen this element, duplicates exist
    if (seen.has(currentElement)) {
        return true;
    }
    
    seen.add(currentElement);
    
    // Recursive call
    return hasDuplicates(arr, index + 1, seen);
}

/**
 * Demo function to show all approaches
 */
function demonstrateFunctions() {
    const testArray = [1, 2, 3, 2, 4, 5, 3, 6, 1];
    
    console.log("Original array:", testArray);
    console.log("Duplicates (method 1):", findDuplicatesRecursive(testArray));
    console.log("Duplicates (method 2):", findDuplicatesRecursiveSlice(testArray));
    console.log("Duplicates with counts:", findDuplicatesWithCounts(testArray));
    console.log("First duplicate:", findFirstDuplicate(testArray));
    console.log("Has duplicates:", hasDuplicates(testArray));
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        findDuplicatesRecursive,
        findDuplicatesRecursiveSlice,
        findDuplicatesWithCounts,
        findFirstDuplicate,
        hasDuplicates,
        demonstrateFunctions
    };
}

// Run demo if this file is executed directly
if (require.main === module) {
    demonstrateFunctions();
}
