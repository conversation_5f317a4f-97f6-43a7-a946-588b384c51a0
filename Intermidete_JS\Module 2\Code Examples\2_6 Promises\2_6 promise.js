//Our first promise. It can only be kept. We execute it, then show the result.
let promise = new Promise( (resolve, reject) => {
    resolve("OK, I kept my Promise.");
})

promise.then( result => {console.log(result)} );


//This promise ends in a rejection.  When we call it, we need to include what happens on a reject/failure.
let brokenPromise = new Promise( (resolve, reject) => {
    reject("Sorry, I did my best, but it wasn't good enough.");
});

//To avoid an error, we need to list out what to do on success and what to do on failure.
brokenPromise.then(success => {console.log(success)},
    failure => {console.log(failure)}
)

brokenPromise.then( resolve => {console.log(resolve)},  reject => {console.log(reject)})


//More Than 5 Promise.  This promise generates a random number and resolves it if it's more than 5.
let moreThanFive = new Promise((resolve, reject) => {

    let number = Math.ceil(Math.random() * 10 ) ;

    if(number > 5){
        resolve(`The number was ${number}, just liked I promised.`);
    }
    else{
        reject(`The Number was ${number}, I let you down.`)
    }
});

moreThanFive.then(success => {console.log(success)},
    failure => {console.log(failure)})

    
//Fixing a promise. This promise will resolve if the number is greater than or equal to 5.
//but it's doomed to fail .the number is always four.
let alwaysFive = new Promise((resolve, reject) => {
    let number = 4;
    if(number >= 5){
        resolve(`The number was ${number}, just liked I promised.`);
    }
    else{
        reject(number);
    }
});

//We run the promise, see that it failed, and then fix the problem. 
//We got creative with the names here to show that they don't matter.
alwaysFive.then(neverGonnaHappen => {console.log(neverGonnaHappen)},
epicFail => {console.log(`Sure the number is ${epicFail + 1} . I didn't cheat at all.`) });
