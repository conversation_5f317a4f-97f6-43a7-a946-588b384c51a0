<!DOCTYPE html>
<html>
    <html>
        <title>Copying Objects </title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body{
                margin:0;
            }

            p{
                background-color: #ccc;
                padding: 2em;
                font-size: 1.3em;
            }

            .flex-row{
                display: flex;
                flex-direction: row;
            }

            .example{
                margin: 0 20px;
                border: 2px solid black;
                font-size: 1.5em;
                min-width: 100px;
                min-height: 100px;
            }

            .title{
                margin: 0 20px;
                border: 2px solid black;
                font-size: 1.5em;
                min-width: 100px;
                font-size: 1em !important;
                padding: 0 !important;
                text-align: center;
            }

            #originalvalue{
                background-color: #437f9b;
                color:white;
                text-align: center;;
            }

            #copyofvalue{
                background-color: #b3660e;
                color:white;
                text-align: center;;
            }

            #originalobject{
                background-color: #9b4343;
                color:white;
                text-align: center;;
            }

            #reference{
                background-color: #9b4343;
                color:white;
                text-align: center;;
            }

            #truecopy{
                background-color: #439b43;
                color:white;
                text-align: center;;
            }

            

        </style>
    </html>
    <body>
        <h1>Copying Objects - Value Types vs Reference Types </h1>
        <p> Open the Console window in the developer console to view output for the script.
            Then, review the .js code to see the examples of value and reference types.
            The objects below are a visual presentation of the outcomes of the code. 
        </p>

        <h3>Value Types</h3>
        <div class="flex-row">
            <p class="title">myNumber</p>
            <p class="title">copy</p>

        </div>
        <div class="flex-row">
            <div id="originalvalue" class="example">
                <p>myNumber</p>
            </div>
            <div id="copyofvalue" class="example"></div>
        </div>

        <h3>Object References</h3>

        <div class="flex-row">
            <p class="title">myObject</p>
            <p class="title">object reference</p>
            <p class="title">trueCopy</p>


        </div>
        <div class="flex-row">
            <div id="originalobject" class="example"></div>
            <div id="reference" class="example"></div>
            <div id="truecopy" class="example"></div>

        </div>

        <script src="4_4_copying_objects.js"></script>
    </body>
</html>