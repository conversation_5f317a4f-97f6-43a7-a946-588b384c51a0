'use strict';
//CODE Quality

//Poor Readability
function update(a,b,x)
{
    if(a === 0.00){
        b = x;
        return b;
    }
}


//Better Reability
function updateCustomerPaymentPlan(accountBalance, currentPlan, newPlan){
    if(accountBalance === 0.00)
    {
        currentPlan = newPlan;
        return currentPlan;
    }

}

function Customer(firstName, lastName, email){
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.isValid = function() {return email != "" ? true : false};
}


//Reliability
//blindly assume data is good and database is available.
function saveCustomerData(firstName, lastName, email)
{
    let customerData = new Customer(firstName, lastName, email);
    SaveToDatabase(customerData);

}

//Better Reliablity
function saveCustomer(firstName, lastName, email){

    let customerData = new Customer(firstName, lastName, email);
    if(customerData.isValid()){
        try{
            SaveToDatabase(customerData);
        }
        catch (error){
            //Code to Handle Error, Send Email Alert, or Retry Here...
        }
    }
}

//Dependancy
//What does this code depend on?
function showCustomerEmail(customer){
    let html = document.getElementById("customer");
    html.textContent = customer.email;
}

//No Dependency
function showEmail(emailAddress, id){
    let html = document.getElementById(id);
    html.textContent = emailAddress;
}