function createObjectFromJSON(jsonData){

    //For the input data, try to parse it as JSON.
    //If successful, use CreateHTML to create the HTML element.
    //On Error, let the user know the text was not valid JSON format

    //Use try to test if this is valid JSON that can be parsed.
    try{

        //Convert the JSON into a JS object with JSON.parse()
        let jsObject = JSON.parse(jsonData);
        createHTML(jsObject);
    }
    //If try throws an error, it's not valid JSON.  Output to the page.
    catch (e){
        let errorText = document.createElement("p");
        errorText = "Not Valid Json Data";
        document.body.append(errorText);
    }

}

function createHTML(object)
{
    let div = document.createElement("div");
    let style = ""
    //We can build the styling text out of the object.  We need to apply CSS syntax of  "property:value;""
    for(let data in object)
    {
        style += `${data}:${object[data]};`
    }
    //We can use style.cssText to assign the styling.
    div.style.cssText = style;
    //Set a min height of 10px, in the case that height is not including the JSON. Otherwise, element may not be
    //visible on the page.
    div.style.minHeight= '10px';
    document.body.append(div);
}