<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=<device-width>, initial-scale=1.0">
    <title>Function Based Inheritance</title>
</head>
<body>
    <h1>Inheritance Using Function Based Approach</h1>

    <p>One of the supposed benefits of OOP is inheritance.  Inheritance allows us to design a new Class \ DatatType using an existing class
        as it's starting point.
    </p> 

    <p>
        When we use inheritance, the originial class \ datatype  is known as the parent class, or the "super" class, and the new class
        is known as the child.
    </p>
    <p>
        The child typically has all of the same properties and methods that it's parent has, plus new functionality. The child can be
        thought of as a specialized version of the parent, or a "twist" on the parent class.
    </p>
    <p>
        When using the function based approach, it's critical to use the .prototype property to establish the relationship between the 
        parent \ child.  It's through the prototype that the inheritance relationship is established.
    </p>
    <p>
        To bring the the properties of the parent, we can use parent.call() in the constructor function.  However, this does NOT establish 
        the parent \ child relationship.
    </p>


    <script src="3_3 Inheritance-Function.js"></script>
</body>
</html>