function Person(firstName, lastName, age, bankaccount){
    
    //public property
    this.firstName = firstName;
    this.lastName = lastName;
    this.age = age;
    
    //private variable
    let _bankAccount = bankaccount

    this.getBankAccount = function(){
        return _bankAccount;
    }

    this.spend = function(amount ){
        if(_bankAccount >= amount){
            _bankAccount -= amount;
        }
        else{
            return "Not enough money in Bank Account";
        }
    }

    this.pay = function(amount){
        if(amount > 0){
           _bankAccount += amount;
        }
        else {
            return "Amount can not be negative";
        }

    }
}