'use strict';
//JSON DATA Example
//In this example, we export JavaScript Objects to JSON formatted Text.
//The end result is the details of our object are avaialble as plain text that can be used by any system that can read JSON text.

//The constructor for the book objects
function Book(title, author, year, genre){
  this.title = title,
  this.author = author,
  this.year = year,
  this.genre = genre
}

//The create book function
function createBook(){
  //Create a new Book Object
  let book = new Book(
    document.getElementById("title").value,
    document.getElementById("author").value,
    document.getElementById("year").value,
    document.getElementById("genre").value
  )

  //display the JSON text on the page, using JSON.stringify(object);

  document.getElementById("jsonText").textContent = JSON.stringify(book);
  console.log (JSON.stringify(book))
}


//More Examples
//Create a basic object
let book = {
  title:"Game of Thrones",
  author: "<PERSON>",
  yearPublished: 1990,
  genre: "fiction"
};
console.log(book);

//Convert the object data to JSON text Data.
console.log(JSON.stringify(book));

//Create a new object
let newBook = 
  {
    title:"Alice in Wonderland",
    author: "Lewis <PERSON>ol"
  };

  console.log(newBook);

  //Convert the object data to JSON text Data.
console.log(JSON.stringify(newBook));

let jsonData = `
{
  "sodaBrand":"Dr. Pepper",
  "Size":"12oz",
  "bottleType":"can",
  "sodaType":"diet",
  "price":"1.99"
}
`;

let soda = JSON.parse(jsonData);
console.log(soda);

let mystring = `{"message":"Hello World"}`
try{
  let myobject = JSON.parse(mystring);
  console.log(myobject);
}
catch (e){
  console.log("JSON Text was not properly formatted.")
}
