<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="html, training, lessons, templates">
    <meta lang="en-US">
    
    <title>JS Template</title>
    <style>
    div{
      box-sizing: border-box;
      width: 500px;
      height:500px;
      background-color: rgb(200,200,200);
      margin: auto;
      border: 2px solid black;
      font-size: 2em;
      text-align: center;
    }

    div p {
      color: purple;
    }


    #btn1{
      width: '75px';
      margin: auto;
      float: right;
      padding: 1%;

    }
    </style>


  </head>


  <body>
    <h1>JS Arrays DOM Example</h1>
    <div id='target'> 
    </div>

  
  <script>
    function execute(){
      var someArray = ["JS Array Element 1", "JS Array Element 2", "JS Array Element 3"]
      var element = document.getElementById("target");
      for(let item of someArray){
        let child = document.createElement("p");
        child.innerText = item;
        element.append(child);
      }
    }
    execute();
  </script>

  </body>
</html>