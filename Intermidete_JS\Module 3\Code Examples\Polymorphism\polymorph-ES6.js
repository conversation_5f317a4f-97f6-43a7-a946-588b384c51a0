class Employee {
    constructor(firstName, lastName, job, payRate){
        this.firstName = firstName;
        this.lastName = lastName;
        this.job = job;
        this.totalPaid = 0;
        this.payRate = payRate;
    }

    pay(){
        this.totalPaid += this.payRate;
        console.log("Paid Employee");
    }
}


class HourlyEmployee extends Employee {
    constructor(firstName, lastName, job, payRate, hoursWorked){
    super(firstName, lastName, job, payRate);
    this.hoursWorked = hoursWorked;
    }

    pay(){
    this.totalPaid += this.payRate * this.hoursWorked;
    console.log("Paid Employee");
    }
}


let hourly = new HourlyEmployee("<PERSON><PERSON>", "<PERSON>", "Data Analysis", 35);
hourly.hoursWorked = 10;
console.log(hourly)
hourly.pay()
console.log (hourly.totalPaid);

console.log(hourly instanceof Employee ) // results true
console.log(hourly instanceof HourlyEmployee ) //results true

console.log(typeof (hourly)); //results object
