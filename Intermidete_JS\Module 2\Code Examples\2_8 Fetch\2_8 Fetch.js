'use strict';
//FETCH
//Does all the work for us.
function makeBadRequest() {
    let url = `https://badrequest`;
    fetch(url).then(success => {

        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = success
    },
    failure => {
        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = failure

    });
}

function makeGoodRequest() {
    let url = `https://randomuser.me/api/?nat=us`;

    fetch(url)  //Fetch the Data from the web server
    .then(response => response.json()) // THEN convert the response to a Js Object with .json()
    .then(object => {  //THEN print the object to the console. Converthe object to pure JSON. Display to page.
        console.log(object);
        let jsondata = JSON.stringify(object);
    
        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = jsondata;
        }

    )





}