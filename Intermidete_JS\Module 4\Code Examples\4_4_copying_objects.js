"use strict";

//value data type
let myNumber = 5;
console.log(`The value of myNumber is ${myNumber} ` );
let copy = myNumber;
console.log(`The value of the copy is ${copy} ` );

myNumber = 1;

console.log(`The value of my number was changed. It's new value is ${myNumber} ` );
console.log(`The value of the copy has not changed. It is ${copy} ` );

function valueTypesExample(inputData){
  inputData += 1;
  return inputData;
}

let returnedValue = valueTypesExample(myNumber);

console.log(`The value of myNumber was NOT changed. A copy of it was passed to the function. myNumber = ${myNumber} ` );
console.log(`The returned value, calcuated by the function is ${returnedValue} ` );

//Object Reference Example
let myObject = {
  data:100
}

console.log(`The value of myObject.data is ${myObject.data} ` );
let someObject = myObject;
console.log(`The value of the someObject is ${someObject.data} ` );

myObject.data = 101;

console.log(`The value of myObject.data was changed. It's new value is ${myObject.data} ` );
console.log(`someObject is a reference to myObject. It points to the same object data. It's value is  ${someObject.data} ` );

function referenceTypeExample(inputObject){
  inputObject.data += 1;
  return inputObject;
}

referenceTypeExample(myObject);
console.log(`myObject.data is ${myObject.data}`);
console.log(`someObject.data is ${someObject.data}`);

//Let trueCopy be an new, empty object.
let trueCopy = {};
//Copy all data to trueCopy from myObject
Object.assign(trueCopy, myObject);

//trueCopy is a full copy
console.log(`trueCopy is a full copy of myObject, created by Object.assign().  It's starting value is ${trueCopy.data}`);
myObject.data = 0;
console.log(`The value of myObject.data was changed to ${myObject.data}`)
//Now changing myObject does not change the trueCopy.
console.log(`Because it's not a reference to myObject, the value of trueCopy has not changed. ${trueCopy.data}`);


document.getElementById("originalvalue").textContent = myNumber;
document.getElementById("copyofvalue").textContent = copy;

document.getElementById("originalobject").textContent = myObject.data;
document.getElementById("reference").textContent = someObject.data;
document.getElementById("truecopy").textContent = trueCopy.data;

