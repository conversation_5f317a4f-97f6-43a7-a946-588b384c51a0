'use strict';
//Class Creation
class Employee {
    //The constructor
    constructor(firstName, lastName, job, payRate){
        //properties of the class
        this.firstName = firstName;
        this.lastName = lastName;
        this.job = job;
        this.totalPaid = 0;
        this.payRate = payRate;
    }

    //methods of the class
    pay(){
        this.totalPaid += this.payRate;
        console.log("Paid Employee");
    }

    promote(newJob){
        this.job = newJob;
    }

}

//Creating an object from the class and using it
let emp = new Employee("<PERSON>", "<PERSON>", "JavaScript Developer", 2500);
emp.pay();
emp.promote("Senior JavaScript Developer")
console.log (emp);
console.log (typeof(emp) ) //object
console.log (emp.constructor.name ) //Employee
console.log (emp instanceof Employee)  //true
