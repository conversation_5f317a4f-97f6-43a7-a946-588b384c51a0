'use strict'

myFunc();
console.log(someVar);
//is undefined because JS doesn't know the value of someVar yet.


//hoisted to the top of the file.
function myFunc(){
    console.log("myFunc has been used.")
}

var someVar= 12; //hoisted to the top of the file. However, 
//you can only do this with keyword var. let will throw an error
//Recommended that you avoid doing this. Declare your variable first with keyword let.
