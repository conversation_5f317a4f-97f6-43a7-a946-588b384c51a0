<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debugging</title>

    <style>
        .flex-row{
            display: flex;
            direction: row;
        }
        .number{
            margin: 10px;
            padding: 10px;
            background-color: rgb(238, 238, 115);
            font-size: 2em;
        }

        button{
            padding: 10px;
            margin: 10px;
            border: none;
            font-size: 1.3em;
            box-shadow: 4px 4px 2px rgb(97, 97, 97);
        }

        .sequence{
            margin: 10px;
            padding: 10px;
            background-color: rgb(68, 103, 156);
            color:white;
            font-size: 2em;
        }

    </style>
</head>
<body>

    <h1>Using Debugging Tools</h1>

    <p>
        In mathematics, Fibonacci numbers form a sequence of numbers where each number is the sum of the two preceding ones.
    </p>
    <p>
        The Fibonacci sequence is very unusual in the pattern is seen over and over again in the the natural world. It also forms
        something called the Golden Ratio, which we use in CSS design to create attractive layouts. 
    </p>
    <p>
        In this lesson, you will learn to use debugging tools by executing an algorithm that generates the fibonacci sequence.
    </p>

    <img src="golden_ratio.png" alt="">
    <div>
        <button id ="btn" onclick ="changeState()">Next Fibonacci Number</button>
    </div>

    <div id="next" class="flex-row">
        <div class="number">Fibonacci Number</div>

    </div>
    <div id="fn" class="flex-row">
        <div class="sequence">Order In Sequence</div>

    </div>

    <script src ="1_3 Debugging.js"></script>

</body>
</html>