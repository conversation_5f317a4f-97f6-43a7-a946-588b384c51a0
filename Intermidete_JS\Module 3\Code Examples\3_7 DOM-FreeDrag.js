
let dragging = false;
let offset = {x:0,y:0};

function beginDrag(object, event){
  event.preventDefault();
  console.log(event);
  object.style.backgroundColor =  "red"; //event.offsetX;
  dragging = true;

  offset.x = object.offsetLeft - event.clientX;
  offset.y = object.offsetTop - event.clientY;

}

function onDrag(object, event){
  event.preventDefault();
  if(dragging == true)
  {
    object.style.left= (event.clientX + offset.x) + 'px';
    object.style.top= (event.clientY + offset.y) + 'px';

    console.log(event.clientX);
  }

}


function release(object, event)
{
  event.preventDefault();
  object.style.backgroundColor =  "darkgreen"; //event.offsetX;

  dragging = false;
}