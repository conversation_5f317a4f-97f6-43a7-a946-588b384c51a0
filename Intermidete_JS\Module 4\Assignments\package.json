{"name": "fizzbuzz-assignment", "version": "1.0.0", "description": "FizzBuzz assignment with unit tests", "main": "fizzbuzz.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["fizzbuzz", "javascript", "testing"], "author": "", "license": "ISC", "devDependencies": {"jest": "^29.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["*.js", "!*.test.js"]}}