"use strict";

//Error Handling Example 3

//Some Text Data.  One is properly formatted, the other is not.
let goodData = '{"name": "<PERSON>", "age": 24, "city": "New York"}'
let badData = '{data is not formated correctly.}' //this data will throw an error when used.

let data = [goodData, badData]
let chance = Math.floor(Math.random() * 2); //Get a random number 0 or 1
let myData = data[chance];
//We put both the good and bad data into an array, and give ourselves a chance to either pull
//the bad data or the good data

//now we do not know which data we will get.
//so we should try to parse the date.

//It's a great idea to use try..catch any time we are working with data that we did not create, such as
//Data recieved from user input forms
//Data recieved from a web server
//Sendig data to a web server (in case server is not available or has issue)

let user = new Object;

try{
  user = JSON.parse(myData);
  //try to create a user object from the raw data.
}
catch (e){
  console.log("An error occured processing the user data.")
  user = {name: "unknown user", age: -1, city: "unknown"}
  //if we get the bad data, we'll handle it, and set our user object to some default values.
}
finally
{
  //regardless of if we get an error, let's output te user object to the console.
  console.log(user);
}

console.log("End of script.")
