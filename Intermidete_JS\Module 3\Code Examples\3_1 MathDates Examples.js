//Cast/Convert Number To Test
let num = 10;
let text = String(num) + "1";
console.log(text);

//ParseInt - Text to Whole Numbers
console.log("ParseInt Text to Whole Numbers")

let getNumber = parseInt("250px");
console.log(getNumber);

let getNumber2 = parseInt("2.9em");
console.log(getNumber2)

//toString binary Conversion
console.log(".ToString Binary Data Conversion")

num = 255;
let binaryNumber = num.toString(2);

console.log(binaryNumber );

//Not A Number Test
console.log("Not a Number Tests")
console.log(isNaN(num) );
console.log(isNaN(binaryNumber) );
console.log(isNaN('This is some text')) ;

//Infinity
console.log("Infinity Tests")
let i = Infinity;
console.log(isFinite(num));
console.log(isFinite(i));

//Rounding
console.log("Rounding and Math Functions" );
let dec = 3.4;
console.log(Math.floor(dec) );
console.log(Math.ceil(dec) );
console.log(Math.round(dec) );

//Expoential
console.log("Exponents")
let powerOfTwo = Math.pow(dec, 2);

console.log(powerOfTwo );
console.log(powerOfTwo.toFixed(2) );

//Dates
console.log("Dates and Times")
let time = new Date()
console.log(time)

let month = time.getMonth();
console.log(month)

let day = time.getDate();
console.log(day);

let year = time.getFullYear();
console.log(year);

let dayOfTheWeek = time.getDay();
console.log(dayOfTheWeek);


let partyLikeIts = new Date(1999, 0, 1);
console.log(partyLikeIts)

