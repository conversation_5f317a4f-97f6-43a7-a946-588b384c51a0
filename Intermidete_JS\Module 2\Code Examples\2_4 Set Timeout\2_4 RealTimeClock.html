<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Spartan&display=swap" rel="stylesheet">
    <title>Set Timeout</title>

    <style>
        #clock{
            background-color: rgb(46, 46, 46);
            color: red;
            padding: 20px;
            text-align: center;
            max-width: 200px;
            font-family: 'Spartan', sans-serif;
            font-size: 3em;
            margin: auto;
            border: 10px solid rgb(163, 19, 19);
            border-radius: 25px;


        }
    </style>
</head>
<body>

    <h1>Real Time Clock </h1>
    <p>The setInterval function will continue to fire a function at a set unit of time until clearInterval is called.  </p>
    <p>This example uses setInterval to get the current time and display it onto the screen every 1 second.</p>
    <div id="clock">
    </div>

    <div>
        <button onclick="stopClock()">Stop Clock</button>
        <button onclick="startClock()">Restart Clock</button>
    </div>


    <script src="2_4 RealTimeClock.js"></script>

    
</body>
</html>