class Patron{
    constructor(firstName, lastName, address, birthday, phone, email)
    {
        this.firstName = firstName;
        this.lastName = lastName;
        this.address = address;
        this.birthday = birthday;
        this.phone = phone;
        this.email = email;
        this.onloan = new Map();
    }

    loan(book){
        this.onloan.set(book.title, book);
    }

    checkIn(book)
    {
        this.onloan.delete(book.title);
    }
}

export {Patron}