'use strict';
//SetTimeOut And CallBacks

//Save our progress Element for future reference
const progressElement = document.getElementById("progress");

//The Load Data function will execute when the button is clicked.
//It will call doWork, passing it a callback function.
function loadData()
{
    
    progressElement.textContent = "Loading";

    doWork( ()=>{
        let e = document.createElement("div");
        e.textContent = "Data has been loaded!"
        document.body.append(e);
    })
    //The call back function creates an new element that lets us know the data is loaded.
    //It will be used in the doWork function once doWork is done.
}



//the doWork function, accepts the callback function
function doWork(callBack){

    //create a loop from 1 to 10
    for(let  i = 0; i < 10; i++)
    {
        //for each step of the look, call setTimeOut.
        //setTimeout will run a function that adds one "." to the loading element
        //If we are on the last run of the loop, perform the call back.
        setTimeout( ()=> {
            progressElement.textContent += ".";
            if(i === 9)
            {
                callBack();
            }
        }, 1000 * i); //We must multiple the timer (1sec by each step of the loop. Else, all setTimeout calls would execute at the same time.)
    }
}


