"use strict";

let string1 = 'single-quote string';
let string2 = "double-quote string";
let string3 = `backtick string`;
let string4 = "100"; //This is a string. The characters are 1, 0, and 0. 
//It won't be treated like a number.
let notAString = 100; //This is a number. It's not wrapped in quotes.

//string interpolation
let weather = "cloudy";
console.log(`The weather today was ${weather}`);
console.log(`2 + 2 is equal to ${2+2}`);
//preferred method, only works with backticks
console.log("The weather today was " + weather);
//more difficult to type and read.

let myString = "abc";
console.log(myString.length);
//Length of 3

let myLongString = "abcdefghijklmnopqrstuvwxyz";
console.log(myLongString.length);
//Length of 26

let anotherString ="abc ef90 @";
console.log(anotherString.length);
//Length of 10. The symbols, empty spaces, and numeric characters all count.


let stringSplit1 ="Use split() to split the text into multiple parts."
let stringSplit2 = "We*can*split*on*any*character";

console.log(stringSplit1.split(" ")) //split on spacebar character.
console.log(stringSplit2.split("*")) //split on * character


let fullString = "abc@123";
let whereIsLetterC = fullString.indexOf("c");
console.log(whereIsLetterC);
//returns 2. 

let mySentence = "the cat in the hat."
console.log(mySentence.indexOf("t") )
//Output is 0. It doesn't keep searching after finding the first match.

let caseSensitive = "The Dog In The Window";
console.log(caseSensitive.indexOf("t") )
//Output is -1.  It didn't find a lowercase "t" in the string. "T" is not the same as "t"

let singleCharacter = fullString[3];
console.log(singleCharacter);
//Output is @
console.log(fullString[0]);
//Ouput is a
console.log(fullString[1]);
//Ouput is b

let pieceOfString = fullString.slice(0,3);
//Get the characters starting at 0, ending at 3. Gets 0, 1, 2. 
//Does not include the character at position 3
console.log(pieceOfString);
//Output abc

let sliceExample2 = fullString.slice(3);
//Get characters starting at index 3, stopping at the end.
console.log(sliceExample2);
//Output @123


let subString = fullString.substring(0,3);
console.log(subString);
//Output abc


//Other Method Calls
let someData ="afjgn4b2wordgf6fx";
let emailAddress = "<EMAIL>";
let formalName = "Mr. John Smith"
let mixedCase = "I was very excitied the English football team won the World Cup."


console.log(someData.includes("word"));
//does someData include the characters "word" somewhere in it?

console.log(emailAddress.endsWith(".com"));
//does emailAddress end with "".com"?

console.log(formalName.startsWith("Mr. "));
//does formalName begin with "Mr. "?

console.log(mixedCase.toLowerCase());
console.log(mixedCase.toUpperCase());
//convert this mixed case sentense to all upper and lower case.


//Special Characters
let linebreak = "This poem \n is broken up \n into several lines."
console.log(linebreak);

let backspace = "Because the backspace key is treated special, we need to use \\ to include a normal backspace."
console.log(backspace);

let quotes = "Because quotes are required for strings, we need  \" special characters \" to include them. "
console.log(quotes);


//Unicode Characters
let musicNote = "\u266A"; //a music note
let funnyEmoji = "\u{1F606}"; //funny emoji
let accentedA = "\u00E0"; //accented a
console.log(musicNote);
console.log(funnyEmoji);
console.log(accentedA);

//DOM Updates
//Writing to the page.
let paragraphElement1 = document.createElement("p");
paragraphElement1.textContent = `The weather today was ${weather}`;
document.body.append(paragraphElement1);

let paragraphElement2 = document.createElement("p");
paragraphElement2.textContent = `2 + 2 is equal to ${2+2}`;
document.body.append(paragraphElement2);

let paragraphElement3 = document.createElement("p");
paragraphElement3.innerText = myLongString + "is the value of myLongString. It's length is " + myLongString.length;
document.body.append(paragraphElement3);

let paragraphElement4 = document.createElement("p");
paragraphElement4.innerText = "Fun with unicode symbols and emojis " +  musicNote;
document.body.append(paragraphElement4);

let paragraphElement5 = document.createElement("p");
paragraphElement5.innerText = funnyEmoji;
document.body.append(paragraphElement5);

let paragraphElement6 = document.createElement("p");
paragraphElement6.innerText = "Non-english characters example : " + accentedA;
document.body.append(paragraphElement6);

let paragraphElement7 = document.createElement("pre");
paragraphElement7.innerText = linebreak
document.body.append(paragraphElement7);
