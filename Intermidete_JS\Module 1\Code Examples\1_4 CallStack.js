//CALL STACK

//Function 1 calls Functions 2 which calls Functions 3.
//3 Calls on the Call Stack.
//Function 3 finshed FIRST, then 2, and then finally Function 1.
//Function 1 has to wait for Function 2 to finsh, which in turn has to wait for Function 3 to finsh


function firstFunction(){
    console.log("Starting FIRST function.")
    secondFunction();
    console.log("FIRST function completed.")
}

function secondFunction(){

    console.log("Starting SECOND function.")
    thirdFunction();
    console.log("SECOND function completed.")
}

function thirdFunction(){
    console.log("Starting THIRD function.");
    console.log("THIRD function completed.");
}


firstFunction();
