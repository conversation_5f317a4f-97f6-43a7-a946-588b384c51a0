"use strict";

let sky = 'blue';

if (sky == 'blue') 
{
  console.log('The sky is blue during the day.');  //WE ARE DONE
}
else if(sky=='black'){
  console.log('The sky is black at night.') //We are done
}
else if(sky=='red'){
  console.log('Maybe it is sunset');
}
else{
  console.log('The sky isn\'t blue or black. Something is very wrong!'); //if nothing was true, do this default thing instead
}

let age = 30;
//shortcut if statement
let canDrive = (age > 16) ? true : false;


canDrive = false;
if(age > 16){
  canDrive = true;
}
else {
  canDrive = false;
}

canDrive = (age > 70) ? 'Senior Driving Detected' : 'Slow Down!!!!'


//switch , case statements
let sodaFlavor = 'orange';
switch(sodaFlavor){
  case 'strawberry':
    console.log('Strawberry Flavored Soda');
    break;  //must include the break; We are DONE. STOP.
  case 'apple':
    console.log('Apple Flavored Soda');
    break;
  case 'orange':
    console.log('Orange Soda');
    break;
  case 'grape':
    console.log('Grape Soda');
    break;
}

for (let i = 0; i < 1000; i++)
{
  if(i === 2)
  {
    break; //break out of the loop
  }

  console.log(`BREAK EXAMPLE: The loop as ran ${i+1} times.`)
}


for (let i = 1; i < 6; i++) {

  if(i === 3){
    console.log("i is equal to 3. Skipping ahead.")
    continue; //stop the loop here, and start again on the next run. STOP HERE AND THEN CONTINUE
  }

  console.log("CONTINUE EXAMPLE: Loop has ran " + i + " times.");

}

//DOM Examples
let guess = prompt("Please try to guess my favorite number, between 1 and 10.")
let guessOutput = document.createElement("div");
guessOutput.classList.add("content")

if(guess == 7){

  guessOutput.innerHTML = `You are right! My Number was  ${guess}!  How did you know?`
  guessOutput.classList.add("guess-right")
  document.body.append(guessOutput); 
}

else  if (isNaN(guess)) {
  guessOutput.innerHTML = `Sorry, it looks like you didn't enter a number. Don't try to trick me!`
  guessOutput.classList.add("guess-wrong")
  document.body.append(guessOutput); 
}

else if (guess < 1)
{
  guessOutput.innerHTML = `You entered a number less than 1. My number is between 1 and 10!`
  guessOutput.classList.add("guess-wrong")
  document.body.append(guessOutput); 

}

else if (guess > 10){
  guessOutput.innerHTML = `You entered a number larger than 10. My number is between 1 and 10!`
  guessOutput.classList.add("guess-wrong")
  document.body.append(guessOutput); 

}

else {
  guessOutput.innerHTML = `No, sorry.  ${guess} isn't my favorite number. Press F5 to try again!`
  guessOutput.classList.add("guess-wrong")
  document.body.append(guessOutput); 

}


//Returning Early

function returnExample(value)
{
    if(value === 1)
    {
        console.log("Returning Early.")
        return;
    }

    console.log("Done!")
}


function checkForBadInput(a, b)
{
    //if a or b is not a number then return early...
    if(isNaN(a) || isNaN(b)) //  the symbol || means OR
    {
        console.log("One input is not a number. Returning.");
        return ;
    }

    //code will run if the IF STATEMENT Was false.
    console.log(a +b);
    return a + b;
}


returnExample(100);
returnExample(1); //returns early
returnExample(5);

checkForBadInput( 2, 2,);
checkForBadInput( "horse", 10); //returns early

//LOGICAL OPERATORS
//OR LOGIC ||  (JS uses the ||) OR
//AND LOGIC  && AND
let test= 5;
let otherTest = 10;
if (test === 5 && otherTest === 4){
  console.log("Both conditions were TRUE");
}

if (test === 5 || otherTest === 4){
  console.log("I will execute because at least one conditon was true");
}

let candy = {
  sweetness: "sweet",
  texture: "crunchy"
}

if (candy.sweetness === "sweet" && candy.texture ==="soft")
{
  //You will buy the candy if it's sweet AND soft
  BuyCandy();
}

if (candy.sweetness ==="sour" || age <= 12)
{
  //You will buy the candy if it's sour OR if yur age is less or equal to 12.
  BuyCandy();
}



//define the BuyCandy Function.
function BuyCandy(){
  console.log("You bought the candy. Yum!");
}