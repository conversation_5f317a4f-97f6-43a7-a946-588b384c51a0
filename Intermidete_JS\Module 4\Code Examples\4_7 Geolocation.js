var loc = document.getElementById("location");

function getLocation() {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(displayLocation);
  } else {
    x.innerHTML = "Geolocation is not supported by this browser.";
  }
}

function displayLocation(location) {
    let lat = location.coords.latitude;
    let long = location.coords.longitude;

    let p = document.createElement("p");
    p.innerText = `Your current location is at latitude ${lat}`;

    let p2 = document.createElement("p");
    p2.innerText = `Your current location is at longitude ${long}`;

    loc.append(p);
    loc.append(p2);

    let link = document.createElement("p");
    link.innerHTML = 'Follow the link to <a href="https://latlongdata.com/latitude-longitude-lookup/" target="_blank"> the Latitude and Longitude Finder  </a> and enter these coordinates to see your location on the map. '

    loc.append(link);

  }

  getLocation();