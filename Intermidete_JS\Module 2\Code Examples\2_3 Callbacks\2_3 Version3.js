'use strict'
//Version 3 - The CallBack Version
//This version is the most complex and the most flexible.

//We have three possible functions for displaying to screen, sending data to web server
//or downloading to a file.  We have mocked up the web server and download functions.
function displayToScreen(data, displayer="loader")
{
    document.getElementById(displayer).textContent = data;
}

function sentToWebServer(data)
{
    console.log("Sending data to web server");
}

function downloadToFile(data){
    console.log("downloading data as PDF file")
}

//doWork accepts a callback function as an arguement. It will perform the callback function
//when it's done doing the work.  It doesn't know/care what the callback function is.
function doWork(n, callbackFunction){
  
    n =  Math.pow(n,3);
    callbackFunction(n)
}

//Now the button itself no longer directly does the work.  It executes doWork, and 
//provides the callback function displayToScreen.  

//We can swap out the callback function here, and immediate change the behavior of the button
//without any other code.  While more complex, this code is the best to work with as the project grows in size.

function execute(n){
    doWork(n, displayToScreen);
}
