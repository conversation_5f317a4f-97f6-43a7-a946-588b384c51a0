'use strict';
//SetTimeOut And CallBacks

//Save our progress Element for future reference
const displayHTML = document.getElementById("display");

function waitForIt(){
    //Get the value of the wait time input element.
    let waitTime = document.getElementById("waittime").value;
    
    //mutliple it by 1000 ms to get proper number of ms to wait.
    let timeout = 1000 * waitTime;

    //call setTimeOut with a function that will update the display text, waiting for the timeout duration.
    setTimeout( () => {
        displayHTML.innerText= "BOOM!"
    }, timeout);

}

