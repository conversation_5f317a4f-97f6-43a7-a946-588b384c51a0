"use strict"

//JS Closure No Object Example
function makeCounter(){
    let count = 0;

    //Closure
    return function(){
        console.log(`Counter was ${count}`)
        return count++;
    }
   
}

let counter = makeCounter();
console.log(counter() );
console.log(counter() );
console.log(counter() );
console.log(counter() );


function PowerOf(number){
    let n = number;
    let power = 1;

    return function(){
        let powerOf = 0;
        let result = n;
        while(powerOf < power){
            result *= n;
            powerOf++;
        }
        power++;
        return result;

    }
}
console.log("Power Of Example")
let getNextPower = PowerOf(2);
console.log(getNextPower() );
console.log(getNextPower() );
console.log(getNextPower() );
console.log(getNextPower() );
console.log(getNextPower() );

//ES5 Constructor Function with private properties
function Cat(name){

    this.catName = name;
    let lives = 9;
    this.useALife = function(){
        return lives = lives - 1;
    }
    this.livesRemaining = function()
    {
        return lives;
    }

}
 
let someCat = new Cat("Felix");
console.log(someCat);
//Es6 Class with private properties

class AnotherCat{
    #privateData = 100;
    
    constructor(name){
        this.name = name;
        let _lives = 9;

        this.useALife = function(){
            return _lives = _lives - 1;
        }
    }

    set data(n){
        
        this.#privateData = n;
    }

    get data(){
        return this.#privateData;
    }



}