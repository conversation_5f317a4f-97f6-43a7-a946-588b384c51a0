"use strict;"

function sendAjaxRequest() {

    document.getElementById("table").style.visibility = 'visible';

    var requestURL = 'https://raw.githubusercontent.com/ACTWebDev/SD101/master/mydata.json';
    var request = new XMLHttpRequest;
    request.open('GET', requestURL);

    request.onreadystatechange = function () {
        console.log(this.readyState + " " + this.status + " " + this.statusText);
        if (this.readyState == 4 && this.status == 200) {
            var jsonData = this.responseText;
            console.log(jsonData);
            var objectData = JSON.parse(jsonData);

            console.log(objectData);

            for(i = 0; i < objectData.products.length; i++)
            {
                let row = document.createElement("tr");
                document.getElementById("table").append(row);

                for (let prop in objectData.products[i]) 
                {
                    let cell = document.createElement("td");
                    cell.innerText = objectData.products[i][prop];
                    row.append(cell);
                    
                }
            }
        }
    }

    request.send();

}