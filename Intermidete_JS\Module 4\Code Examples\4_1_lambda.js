'use strict;'
//LAMBDA Functions
let data = "Some Data";

//A basisc, normal function.
function basic(input)
{
  console.log(`I'm a function that logs input, ${input}`);
}

basic(data);

//A lambda function, uses ()=>  
//Has no name.
//Useful for;
  //when you don't want to have another named function.
  //for short, quick lines of code that need the power of a function
  //for when you need to box a function up and use it as input.

 const lambdaExample = (input)=> {console.log(`I'm a lambda function that logs input, ${input}`) } ;



//Create a const, assign it a lambda function that accepts an "input" argurment.
 //I can be stored inside of var, let , or const, and reused later..
lambdaExample(data);


//More examples

const x = () => {return 3 * 5};
const y = (myInput) => {return myInput * 10};
const z = (arg1, arg2) => {return arg1 + ' ' + arg2 } ;

console.log ( x()  ); // 15
console.log ( y(10) ); //100
console.log ( z('Hello','world') ); //"Hello world"



//Even number example
//For a input called number, return true if the number can be evenly divided by 2.
function isEvenNumberFunction(number){
  return number % 2 === 0 ;
}


//The lambda version 
//Create a function. We won't give it a nae. For an input called number, return true if the number can be evenly divided by 2.
//assign the function to a const variable called isEvenLambda 
const isEvenLambda = (number) => {return number % 2 === 0};

//Short Syntax.  When a lambda sits on a sigle line, we can remove the () and the {}. We can also remove the return.
//This code says "Create a function. We won't give it a name. It accepts an input called number. Return true if the number can be evenly divided by 2
const isEvenNumber = number => number % 2 === 0

console.log ( isEvenNumber(2) );
console.log ( isEvenNumber(10) );
console.log ( isEvenNumber(7) );


//Lambda are a good way to have "portable code" that you can reuse later.  Here our lambdaExample logs the values
//of this array for us.
let myArray = ['Red','Blue','Green','Yellow']
{
  for(let item of myArray)
  {
    lambdaExample(item);
    //for each item in myArray, execute the lambda function, logging it to the console.
  }
}

//Let's great an object with properties a nd b
let myObject = {a: 100, b: 200};

//property c will be a lambda function that takes in the object as input, and returns properties a + b.
myObject.c = (myObject)=> {return myObject.a + myObject.b};

//when we execute property c, and pass in the object, we get the calcuated results.
console.log(myObject.c(myObject));

//Going crazy, we've added a propety d, which is a lambda that adds a + b and executes c.  Inception!
myObject.d = myObject => {return myObject.a + myObject.b + myObject.c(myObject)};
console.log(myObject.d(myObject)); 


//Using Lambda as a shortcut.  We've shorted console.log down to just log. Saves time!
const log = (x)=>{console.log(x)};
log("Some Information");
log("Some Text");

//One more example.  Lambda are shortcuts that save time.
let today = "Weds";

const isItPayDay = (day)=> {return day === 'Friday' ?  true : false};
log(isItPayDay(today) );
//In one line of code....
  //Create a const called isItPayDay and assign it a lambda function..
  //The lambda function accepts the arguement day, (we don't care what day is called, never gets used again)
  //and it does the following with it..
  //return the results of ...
  //if day is equal to Friday then true, else false.


//even shorter syntax.  
const payday = x => x === 'Friday' ? true : false;
//a single line lambda function with a single arguement doesnt need the () or the {} or the return.
//In one really short line of code, ...
 //Create a const called payday and assign it  a lambda function..
  //The lambda function accepts the arguement x, (we don't care what x is called, never gets used again)
  //and it does the following with it..
  //return the results of ...
  //if x is equal to Friday then true, else false.
log(payday(today))


//vs//
function paydayLongVersion (someDay){
  if (someDay === "Friday")
  {
    return true;
  }
  else
  {
    return false;
  }
}
log(paydayLongVersion(today));
// ()=> {  }

