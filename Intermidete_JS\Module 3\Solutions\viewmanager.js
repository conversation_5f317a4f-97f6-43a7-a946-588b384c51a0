class ViewManager{
    constructor(root, library)
    {
        this.root = root;
        this.library = library;
    }

    //Clears all elements on the root element.
    clearPage(){
        this.root.innerHTML = "";
        this.hideBookForm();
        this.hidePatronForm();
    }

    //Book Updates

    //Get all books from the library and display on the page.
    refreshCollection(){
        //clear the entire page.
        this.clearPage();

        //display the column headers
        let div = document.createElement("div");
        div.className = "record";
        div.innerHTML = `<div> Title </div>
        <div> Author </div>
        <div> Year </div>
        <div> Genre </div>
        <div> Copies Owned </div>
        <div> On Loan </div>`;
        this.root.append(div);

        //display each book in the collection on page
        let collection = this.library.getBookCollection();
        if(collection.size > 0)
        {
            for(let book of collection){
                this.displayBook(book);
            }
        }

        //show the book update form
        this.showBookForm();
    }

    //Load single book record onto the page.
    displayBook(book)
    {
        let div = document.createElement("div");
        div.className = "record";
        div.innerHTML = `<div> ${book[1].title}</div>
        <div> ${book[1].author}</div>
        <div> ${book[1].year}</div>
        <div> ${book[1].genre}</div>
        <div> ${book[1].copies}</div>
        <div> ${book[1].onLoan}</div>
        <button class="btn" onClick="view.updateBookForm('${book[1].title}')"> Edit </button>
        `;

        if(book[1].copies == book[1].onLoan){
            div.innerHTML += '<div> Unavailable </div>'
        }
        else{
            div.innerHTML += `<button class="btn" onClick="view.displayCheckout('${book[1].title}')"> Checkout </button>`;

        }


        this.root.append(div);
    }

    //Shows the Book Form window
    showBookForm()
    {
        document.getElementById("bookformwindow").className ="show";
    }

    hideBookForm(){
        document.getElementById("bookformwindow").className ="hide";

    }

    updateBookForm(title)
    {
        let book = this.library.getBook(title);

        document.forms.bookform.title.value = book.title;
        document.forms.bookform.author.value  = book.author;
        document.forms.bookform.year.value  = book.year,
        document.forms.bookform.genre.options.selected = book.genre;
        document.forms.bookform.copies.value  = book.copies;

    }

    //Add a new book to the Library from the form data
    addBookToLibrary(){
        this.library.addBook(
            document.forms.bookform.title.value,
            document.forms.bookform.author.value,
            document.forms.bookform.year.value,
            document.forms.bookform.genre.value,
            document.forms.bookform.copies.value,
        )
        this.resetBookForm();
        this.refreshCollection();
    }

    //Reset form data
    resetBookForm(){
        document.forms.bookform.reset();
    }


    //Patrons

    refreshPatrons(){
        //clear the entire page.
        this.clearPage();

        //display the column headers
        let div = document.createElement("div");
        div.className = "record";
        div.innerHTML = `<div> First Name </div>
        <div> Last Name </div>
        <div> Email </div>
        <div> Phone </div>
        `;
        this.root.append(div);

        //display each book in the collection on page
        let list = this.library.getAllPatrons();
        if(list.size > 0)
        {
            for(let patron of list){
                this.displayPatron(patron);
            }
        }

        //show the book update form
        this.showPatronForm();
    }

    //Load single book record onto the page.
    displayPatron(patron)
    {
        let div = document.createElement("div");
        div.className = "record";
        div.innerHTML = `<div> ${patron[1].firstName}</div>
        <div> ${patron[1].lastName}</div>
        <div> ${patron[1].email}</div>
        <div> ${patron[1].phone}</div>
        <button class="btn" onClick="view.updatePatronForm('${patron[1].email}')"> Edit </button>
        `;

        this.root.append(div);
    }

    //Shows the Book Form window
    showPatronForm()
    {
        document.getElementById("patronformwindow").className ="show";
    }

    hidePatronForm()
    {
        document.getElementById("patronformwindow").className ="hide";
    }

    updatePatronForm(email)
    {
        let patron = this.library.getPatron(email);

        document.forms.patronform.firstName.value = patron.firstName;
        document.forms.patronform.lastName.value  = patron.lastName;
        document.forms.patronform.phone.value  = patron.phone,
        document.forms.patronform.email.value  = patron.email;
        document.forms.patronform.birthday.value  = patron.birthday;
        document.forms.patronform.street.value = patron.address.street;
        document.forms.patronform.city.value = patron.address.city;
        document.forms.patronform.state.value = patron.address.state;
        document.forms.patronform.zip.value = patron.address.zip;

        let booksOnLoan = document.getElementById("onLoan");

        for (let book of patron.onloan){
            let opt = document.createElement("option");
            opt.value = book[1].title;
            opt.innerHTML = book[1].title;
            booksOnLoan.append(opt);

        }
        

    }

    //Add a new book to the Library from the form data
    savePatron(){

        let address = this.library.newAddress(
            document.forms.patronform.street.value,
            document.forms.patronform.city.value,
            document.forms.patronform.state.value,
            document.forms.patronform.zip.value,

        )


        this.library.addPatron(
            document.forms.patronform.firstName.value,
            document.forms.patronform.lastName.value,
            address,
            document.forms.patronform.birthday.value,
            document.forms.patronform.phone.value,
            document.forms.patronform.email.value,

        )
        this.resetPatronForm();
        this.refreshPatrons();
    }

    //Reset form data
    resetPatronForm(){
        let booksOnLoan = document.getElementById('onLoan');
        booksOnLoan.innerHTML = "";
        document.forms.patronform.reset();
    }

    //Checkout
    displayCheckout(title){
        this.hideBookForm();
        document.getElementById('checkoutformwindow').className = "show";
        let patrons = this.library.getAllPatrons();

        let customerSelect = document.getElementById('patronSelect');
        customerSelect.innerHTML = "";

        for(let p of patrons){
            let opt = document.createElement("option");
            opt.value = p[1].email;
            opt.innerHTML = `${p[1].firstName} ${p[1].lastName}`
            customerSelect.append(opt);

        }

        let checkoutBtn = document.getElementById("checkout");
        checkoutBtn.dataset.title = title;

    }

    closeCheckout(){
        document.getElementById('checkoutformwindow').className = "hide";

    }

    requestCheckOut(title){
        let email = document.getElementById("patronSelect").value;
        this.library.checkout(title, email);
        this.closeCheckout();
        this.refreshCollection();
    }

    requestCheckIn(){
        let title = document.getElementById("onLoan").value;
        let email = document.getElementById("patronSelect").value;
        this.library.checkIn(title, email);
        this.resetPatronForm();
    }

}



export {ViewManager};