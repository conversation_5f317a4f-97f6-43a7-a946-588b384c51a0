<!DOCTYPE html>
<html>

<head>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron&display=swap" rel="stylesheet">

  <style>
    body,
    html {
      margin: 0;
    }

    main{
      margin: 0;
      min-height: 100vh;
      background-image: linear-gradient(135deg, rgb(255, 255, 255), rgb(90, 90, 90));

    }

    .calculator {
      width: 500px;
      margin: 20px auto;
      background-color: rgb(225, 225, 225);
      border: 4px solid black;
      box-shadow: 10px 10px 10px rgb(85, 85, 85);
      border-radius: 5%;
    }

    .calculator-top {
      padding: 3em;
      border-bottom: 2px solid black;
    }

    .calculator-display {
      background-color: rgb(150, 150, 150);
      width: 90%;
      margin: auto;
      padding: 2px;
      text-align: end;
      font-size: 3em;
      font-family: 'Orbitron', sans-serif;
      border: 2px solid black;


    }

    .calculator-body {
      display: grid;
      grid-template-rows: repeat(4, 1fr);
      grid-template-columns: repeat(4, 1fr);
    }

    .calc-button {
      background-color: white;
      border: 2px solid black;
      font-size: 3em;
      border-radius: 10%;
      text-align: center;
      margin: 20px;
      box-shadow: 2px 2px rgb(100, 100, 100);
    }

    .calc-button-op {
      background-color: rgb(27, 135, 185);
      color: white;
    }

    .span-two {
      grid-column: 1/ span 2;
    }

    .align-end {
      grid-column: 4;
    }


    label {
      font-size: 1.2em;
      margin: 10px;
    }

    h1 {
      background-color: black;
      color: white;
      padding: 10px;
      text-align: center;
      letter-spacing: .5px;
      
    }
  </style>
</head>

<body>


  <main>
    <h1>JS Calculator App</h1>
    <div id="target" class="calculator">
      <div class="calculator-top">
        <div id="display" class="calculator-display">
          0
        </div>

      </div>
      <div class="calculator-body">
        <div class="calc-button" onclick="input(this)">7</div>
        <div class="calc-button" onclick="input(this)">8</div>
        <div class="calc-button" onclick="input(this)">9</div>
        <div class="calc-button calc-button-op" onclick="add()"> +</div>
        <div class="calc-button" onclick="input(this)">4</div>
        <div class="calc-button" onclick="input(this)">5</div>
        <div class="calc-button" onclick="input(this)">6</div>
        <div class="calc-button calc-button-op" onclick="subtract()"> -</div>
        <div class="calc-button" onclick="input(this)">1</div>
        <div class="calc-button" onclick="input(this)">2</div>
        <div class="calc-button" onclick="input(this)">3</div>
        <div class="calc-button calc-button-op" onclick="multiply()"> *</div>
        <div class="calc-button" onclick="input(this)">0</div>
        <div class="calc-button calc-button-op" onclick="reset()"> C</div>
        <div class="calc-button calc-button-op" onclick="displayTotal()"> =</div>
        <div class="calc-button calc-button-op" onclick="divide()"> %</div>


      </div>
    </main>

  <script src="4_5_DOM_CalculatorApp.js"></script>

</body>

</html>