'use strict';
//Object Creation without Class keyword

//The constructor function for the object type
function Employee(firstName, lastName, job, payRate)
{
    //properties of the object type
    this.firstName = firstName;
    this.lastName = lastName;
    this.job = job;
    this.totalPaid = 0;
    this.payRate = payRate;
    //methods of the object type
    this.pay = function(){
        this.totalPaid += this.payRate;
        console.log("Paid Employee");
    }
    this.promote = function(newJob){
        this.job = newJob;
    }
}

//Creating an object from the constructor function and using it
let myEmployee = new Employee("<PERSON>", "<PERSON>", "JavaScript Developer", 2500);
myEmployee.pay();
myEmployee.promote("Senior JavaScript Developer")
console.log (myEmployee);
console.log (typeof(myEmployee) ) //object
console.log (myEmployee.constructor.name ) //Employee
console.log (myEmployee instanceof Employee)  //true
