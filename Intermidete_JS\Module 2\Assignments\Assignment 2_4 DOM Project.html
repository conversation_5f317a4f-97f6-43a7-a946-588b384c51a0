<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assignment DOM 2</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Inter:wght@300&display=swap"
        rel="stylesheet">
    <style>
        body,
        html {
            font-family: 'Inter', sans-serif;
            margin: 0;
        }

        main {
            min-height: 100vh;
        }

        header {
            font-size: 4em;
            text-align: center;
            background-image: linear-gradient(to right, rgb(145, 182, 231), rgb(190, 203, 219));
            padding: 1em;
            font-weight: bold;
            color: black;
            font-family: 'Bebas Neue', cursive;
            letter-spacing: 2px;
        }

        ul {
            list-style-type: none;
        }

        ul li {
            padding: 10px;
            font-size: larger;
        }

        .lead {
            font-size: 1.2em;
            line-height: 1.5em;
        }

        .flexbox {
            display: flex;
            flex-direction: row;
            margin: 0;
            padding: 0;

        }

        .panel {
            flex: 1 0 60%;
        }

        section {
            padding: 15px;
        }

        .image-container {
            border: 5px solid rgb(190, 203, 219);
            flex: 1 0 40%;
            margin: 0;
            padding: 0;

        }

        #cover-image {
            object-fit: cover;
            height: 100%;
            width: 100%;

        }

        .start-button {
            background-color: rgb(9, 93, 203);
            max-width: 250px;
            border-radius: 10px;
            padding: 20px;
            border: 4px solid rgb(1, 1, 145);
            text-align: center;
            color: white;
            font-size: 1.1em;
            box-shadow: 4px 4px 6px rgb(3, 3, 53);
            margin: 20px;
        }

        a {
            text-decoration: none;
        }

        footer {
            background-color: rgb(9, 169, 203);
            color: white;
            padding: 10px;
        }
    </style>
</head>

<body>
    <main>
        <h1 id="assignment">Assignment 2_4 DOM Part 2</h1>

        <header id="company">
            Your Company.Com
        </header>

        <div class="flexbox">
            <div class="panel">
                <section id="about">

                </section>


                <section id="leadership">
                    <ul id="ul-1">
                    </ul>
                    <a id="link" href="#"></a>
                </section>

                <div id="extra">

                </div>
            </div>
            <div class="image-container">
                <img id="cover-image" src="" alt="Suceessful Business Leader">
            </div>
        </div>
    </main>

    <footer>
        <p>Photo by The Lazy Artist Gallery from Pexels</p>
    </footer>
    <script src="Assignment 2_4.js"></script>


</body>

</html>