"use strict";



function flip(){

    let animate = document.getElementById("coin")
    animate.classList.add("coinflip");
    let output = document.getElementById("output");
    output.innerHTML = "";

    let html = document.createElement("p");
    const coinflip = new Promise( (resolve, reject) => {
    
        setTimeout( ()=> {
            let coin = 0;
            coin = Math.random();
            if(coin < .5 )
            {
                animate.classList.remove("coinflip")
                reject("Tails!" );
            }
            animate.classList.remove("coinflip")
            resolve("Heads!");
        } , 1000);
    
    
    
    });

    coinflip.then( success => {html.innerText = success; output.append(html)},
        reject => {html.innerText = reject; output.append(html)} );
        
}