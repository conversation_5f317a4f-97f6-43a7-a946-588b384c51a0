"use strict";
const NEW_PARAGRAPH_STYLE = "red-italic"
const LIST_STYLE = "list-style"

console.log(document.childNodes);

//Get all elements with the p tag, then change them all.
let paragraphs = document.getElementsByTagName("p");
for (let p of paragraphs)
{
  p.classList.add(NEW_PARAGRAPH_STYLE)
}


//Get a single element element by ID, then update all of it's children.
let ul = document.getElementById("ul-1");
let children = ul.children;
console.log(ul.children);

for(let i=0; i< children.length; i++)
{
  children[i].classList.add(LIST_STYLE);
}

//Get all elements by class name, then update them
let someElements = document.getElementsByClassName("simple-class")
console.log(someElements)
for(let element of someElements)
{
  element.style.backgroundColor = "green";
  element.style.color = "white";
  element.style.fontSize = "2em";
}


//Get all elements by it's CSS Rule, then update them
let myElements = document.querySelectorAll("#selector-example div p")
console.log(myElements);
for (let e of myElements)
{
  e.style.color = "blue";
}

//Getting an elment, working with it's children, additonal example
//This example shows us removing a class from the HTML class list and adding a different one.
let parent = document.getElementById("flex-parent");
for(let i=0; i < parent.children.length; i++)
{
  parent.children[i].classList.add("flex-item");
  parent.children[i].classList.remove("removal-class");
}