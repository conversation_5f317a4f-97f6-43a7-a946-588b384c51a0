'use strict'

//This function will be used as a callback.
function myCallback(){
    let time = new Date();
    console.log(`The call back function has been executed at ${time}`);
}

//This is just a plain function.
function simpleFunction(){
    console.log(`A simple function has been executed.`)
}

//This is a plain function that uses a callback.
function useCallback(callback){
    let time = new Date();
    console.log(`The initial function has been executed at ${time}`);
    callback();
}

//This function will run async, because it uses setTimeout().
function asyncFunction(callback){
    let time = new Date();
    console.log(`The async function has been executed at ${time}`);
    setTimeout(callback, 5000); //time in milliseconds
}

//Connected to HTML button.
function executeNormal(){
    useCallback(myCallback);
    simpleFunction();
    simpleFunction();
    simpleFunction();
}

//Connected to HTML Button.
function executeAsync(){
    asyncFunction(myCallback);
    simpleFunction();
    simpleFunction();
    simpleFunction();
}

//Oven Example
//The setup function.
function turnOvenOn(time){
    let startime = new Date();
    console.log(`Turned the oven on at ${startime}`);
    setTimeout(ovenIsReady, time);
}

//The call back function.
function ovenIsReady(){
    let endtime = new Date();
    console.log(`The oven is ready at ${endtime}`);
}

//The execution function, connected to HTML button.
function startOven(){
    turnOvenOn(10_000); //oven will be ready in 10 seconds.
}

//Cancel Set Timeout Example;
//The request for the Taxi. 
function requestTaxi(cabride, cancelTaxi, arrivaltime){
    
    //the maxium time we are willing to wait
    let maxWaitTime = document.getElementById("maxwaittime").value * 1000;;

    let startTime = new Date();
    console.log(`Hailed Taxi at ${startTime}`);
    
    //schedule the cab ride.
    let timeoutID = setTimeout(cabride, arrivaltime);
    
    //schedule the cancellation;
    setTimeout(cancelTaxi, maxWaitTime, timeoutID, maxWaitTime, arrivaltime );
}

function cancelTaxi(timeoutID, maxWaitTime, arrivaltime){
    if(maxWaitTime < arrivaltime){
        clearTimeout(timeoutID);
        console.log("The Taxi has been canceled.")
    }
}

//The callback function
function cabRide(){
    console.log(`Began trip`);
}

//The execution.
//request the taxi, then take the cab ride. We will wait for 10 seconds.
function taxi(){
    let cabArrivalTime = document.getElementById("arrivaltime").value * 1000;
    requestTaxi(cabRide, cancelTaxi, cabArrivalTime);
}