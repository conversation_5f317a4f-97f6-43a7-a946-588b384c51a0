'use strict';
//Ajax Examples
//The call back function, which displays the JSON Data recieved from the web server onto the page.

let nextBlogPost = 1;


function displayContent()
{
    if(this.readyState === 4 && this.status === 200){
        let title = document.createElement("h2");
        let blog = JSON.parse(this.responseText)
        title.innerText =  blog.title;
        document.body.append(title);

        let post = document.createElement("p");
        post.innerText =  blog.body;
        document.body.append(post);
        nextBlogPost++;

    }
}

//The AJAX Request. Uses the call back for onreadyStateChange
function ajaxRequest(callback){
    let request = new XMLHttpRequest();
    let url = `https://jsonplaceholder.typicode.com/posts/${nextBlogPost}`
    request.onreadystatechange = callback;

    request.open("GET", url);
    request.send();
}

//The execution, placed on the HTML button
function requestContent(){
    ajaxRequest(displayContent);
}