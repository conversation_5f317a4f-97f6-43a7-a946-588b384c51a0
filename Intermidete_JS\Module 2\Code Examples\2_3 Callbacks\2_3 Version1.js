'use strict'
//This version is simple.  
//Take a number as input, do some work with it, and display it to the screen.
//This function has TWO TASKS. Do the work, THEN display it to the screen.

//POTENTIAL PROBLEMS
//1. This code is dependent on an element with ID loader being on the page. The code will break
//if there is no HTML loader element on the page.

//2. What if as the code becomes more complex, I want to display new data on the loader element? 
//We should consider splitting the into two functions. One that does the work, and one that displays the data.
function doWork(n){
    n =  Math.pow(n,3);
    document.getElementById("loader").textContent = n;
}


