'use strict';

/*
There is no DOM output  for this code example.  Read the JS code very carefully and make sure
you understand the concept of scope. This lesson is all about how the JS code is structured.
*/

//Global Data
let globalData = 1;

//Use Global Data
//This function uses our variable global data, and depends ons it.
//But it doesn't change our global data.
//This isn't very good -- the outcome of this function depends the current value of global data
function useGlobalData(amount){
 return globalData + amount;
}

//New Data Returned
console.log(useGlobalData(2) );
//Global Data didn't change.
console.log(globalData)

//Modify Global Data
//This function also depends on our globalData, and changes it. 
//This can be very dangerous
function modifyGlobalData(amount){
  globalData += amount;
  return globalData;
 }

 
 //New Data Returned
 console.log(modifyGlobalData(4) );
 //And Global Data was Changed.
 console.log(globalData)

 //See how the results of Use Global Data are different now?
 useGlobalData(2);
//New Data Returned
 console.log(useGlobalData(2) );
 console.log(globalData)

//This Function is "Pure". It doesn't depend on the state of global data
function pure(input){
  return input + 1;
}

//We can pass it globalData if we needed to.
console.log(pure(globalData) );
//Or Anything else.
console.log(pure(10) );


//FUNCTIONAL SCOPE -- Only Exists within the Function
function createADog(name, age, breed)
{
  let newDog = {
    name:name,
    age:age,
    breed:breed,
  }

  let functionScope = "I live only in the function";

  return newDog;
}

let someDog =  createADog("Honey", 1, "Poodle") 
let anotherDog = createADog("Sam", 2, "PitBull") 
console.log(someDog);
console.log(anotherDog)
//console.log(newDog); newDog doesn't exist outside of the createADog function.  
//throws an error. This is a good thing!

//console.log(functionScope); //functionScope doesn't exist outside of the createADogFunction.
//throws an error. This is a good thing!


//Block Scope
let myColors =["red","blue","green"];
for(let color of myColors){
  let paintBucket = {
    size: "Half Gallon",
    paintcolor:color
  }
}


//console.log(paintBucket.paintcolor); //paintBucket doesn't exist outside of the for loop
//throws an error. This is a good thing!

let myPaintBuckets = new Array();

for(let color of myColors){
  let paintBucket = {
    size: "Half Gallon",
    paintcolor:color
  }
  //push the block level object into the global array.
  myPaintBuckets.push(paintBucket);
}
console.log(myPaintBuckets)

//THE ISSUE WITH VAR
//VAR IGNORES BLOCK LEVEL SCOPE - CONTINUES TO EXIST AFTER BLOCK
//VAR ALLOWS FOR REDECLARATION - RECREATED AGAIN EACH TIME IN THIS LOOP. 


for(let color of myColors){
  var  paintBucket = {
    size: "Half Gallon",
    paintcolor:color
  }

  var artifact = "I should have been cleaned up after the for loop block ended."
}

console.log(paintBucket);
console.log(artifact);