'use strict'
//Version 4 - The CallBack Version with a Lambda function
//In this version, our extra call back functions are gone.
//This version may or may not be what you want, depending on how reuseable you need displayToScreen, downloadToFile to be.  
//It's less complex because there are less total functions.
//It's more complex to understand because of the lambda

//doWork accepts a callback function as an arguement. It will perform the callback function
//when it's done doing the work.  It doesn't know/care what the callback function is.
function doWork(n, lambda){
  
    n =  Math.pow(n,3);
    lambda(n) //the lambda function, given the value of n.
}

//Now the button itself no longer directly does the work.  It executes doWork, and 
//provides the callback function displayToScreen.  

//Let's doWork, passing it the value of n.
//When work is done, run the anonymous function listed here.
//The anonymous function accepts a value of x as input and updates the displayer.

function execute(n, displayer){

    doWork(n, (x)=> {
        document.getElementById(displayer).textContent = x;
    });
}
