function CoffeeMug(size, color, phrase)
{
    this.size = size;
    this.color = color;
    this.phrase = phrase
    this.isFull = false;

}
//Anything that uses CoffeeMug as it's prototype will be able to fill()
CoffeeMug.prototype.fill = function() {this.isFull = true};


function StainlessSteelMug(){
  this.size = "large";
  this.material = "steel";
 
}
StainlessSteelMug.prototype = CoffeeMug.prototype;
StainlessSteelMug.prototype.drop = function() {console.log ("Won't break when dropped!")};

let steelMug = new StainlessSteelMug();
steelMug.fill()
console.log(steelMug )
console.log(steelMug instanceof CoffeeMug )
steelMug.drop();
console.log(Object.getPrototypeOf(steelMug));

function Shape(color)
{
    this.color = color;

}
Shape.prototype.draw = function(){ console.log ("You Draw A Shape")};


function Rectangle(color)
{
    Shape.call(this, color);
    this.sides = 4;
}
Rectangle.prototype = Shape.prototype;

let rect = new Rectangle("red");
console.log(rect )
console.log (Object.getPrototypeOf(rect) );
console.log (rect instanceof Shape );
rect.draw();
