<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JS Library App</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <nav>
        <header>JavaScript Library App</header>
        <ul>
            <li onclick="view.refreshCollection()">Browse Collection</li>
            <li onclick="view.refreshPatrons()">Browse Patrons</li>
        </ul>
    </nav>

    <div class="flex-container">
        <!-- The root div is the area of the page that will be updated by JavaScript -->
        <div id="root">

        </div>

        <!-- Holds the hidden book form for editting/saving books -->
        <div id="bookformwindow" class="hide">
            <form id="bookform" action="javascript:void(0)">
                <label for="title">Title</label>
                <input id="title" type="text">
                <label for="author">Author</label>
                <input id="author" type="text">
                <label for="year">Year</label>
                <input id="year" type="number" min="0" max="2022">
                <label for="genre">Genre</label>
                <select name="genre" id="genre">
                    <option value="Ficton">Fiction</option>
                    <option value="Non Fiction">Non Fiction</option>
                </select>
                <label for="copies">Copies</label>
                <input id="copies" type="number" min=0>
                <button id="bookSave" class="btn" onclick="view.addBookToLibrary()"> Save</button>
                <button id="bookReset" class="btn reset" onclick="view.resetBookForm()"> Reset</button>
            </form>

        </div>
        <!-- Holds the hidden form for editting/saving patrons  -->
        <div id="patronformwindow" class="hide">
            <form id="patronform" action="javascript:void(0)">
                <label for="firstName">First Name</label>
                <input id="firstName" type="text">

                <label for="lastName">Last Name</label>
                <input id="lastName" type="text">

                <label for="birthday">Birth Day</label>
                <input id="birthday" type="date">

                <label for="phone">Phone</label>
                <input id="phone" type="phone">

                <label for="email">Email </label>
                <input id="email" type="email">


                <label for="street">Street Address</label>
                <input id="street" type="text">

                <label for="city">City</label>
                <input id="city" type="text">

                <label for="state">State</label>
                <input id="state" type="text">

                <label for="zip">Zip</label>
                <input id="zip" type="text">

                <!-- List of books checked out to the patron -->
                <label for="onLoan">Books On Loan</label>
                <select name="onLoan" id="onLoan"></select>

                <button id="checkInBook" class="btn" onclick="view.requestCheckIn()"> Check In Book</button>
                <button id="savePatron" class="btn" onclick="view.savePatron()"> Save Patron </button>
            </form>

        </div>
        <!-- Holds the hidden checkout form  -->
        <div id="checkoutformwindow" class="hide">
            <form id="checkoutform" action="javascript:void(0)">
                <label for="patronSelect">Select Patron</label>
                <select name="patronSelect" id="patronSelect"></select>
                <button id="checkout" id="checkout" data-title="" class="btn" onclick="view.requestCheckOut(this.dataset.title)">Check Out</button>
            </form>
        </div>

    </div>
    

    <script src="app.js" type="module"></script>
</body>
</html>