"use strict";
//Simple Prototype Example


//Let's create a person object.  The person can perform the following actions: read, write, and sleep.
//Notice that we did not define any data properties, only methods.
let person = {
    read: function() {console.log("I can read.")},
    write: function() {console.log("I can write")},
    sleep: function() {console.log("I can sleep.")}
}

//Now, let's create a student object.  The student has firstName, lastName, age, and grade properties.
//The student's prototype is the person object.  We use the person object as the initial blueprint for the student.
let student = {
    firstName: "Good",
    lastName: "Student",
    age: 25,
    grade: "A",
    __proto__:  person //The prototype for a student is the person object.
}

console.log (student);
//When we examine the student, we see the four properties we expect to see.

//However, the student can read, write, and sleep, because it's prototype was the person object.
//It can do everything a person could.
student.read();
student.write()

//We can examine the prototype of the student with Object.getPrototypeOf, and we see the "person object".
console.log (Object.getPrototypeOf(student));

//The student can do all the things the person can, because the student's prototype property came from Person.
//"person" is a prototype for creating a student.

//Let's create a teacher object, and set it's prototype to person as well.
let teacher = {
    firstName: "Great",
    lastName:"Teacher",
    favoriteSubject: "Math and Science",
    __proto__: person
}

//The teacher object can do all of the things the person object can do.
teacher.read()
teacher.sleep()