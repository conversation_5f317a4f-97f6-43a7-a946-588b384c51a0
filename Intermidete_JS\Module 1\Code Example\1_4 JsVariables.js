"use strict";
var info = "JS variables hold data. They can be assigned with the keyword var.";
console.log(info);

let moreInfo = "Variables can also be created with keyword let. We recommend using let ";
console.log(moreInfo);

let someData = 10;
console.log("We've created someData and assigned  the value 10");
console.log(someData);

someData = 999;
console.log("Variables are mutable, they can be changed. We've changed the value of someData.");
console.log(someData);

console.log("Once you have created a variable with the keyword, you can use it.")
let someNumber = 99;

console.log("Subtract the value of someNumber from someData;")
console.log(someData - someNumber);

const CANNOT_BE_CHANGED = "A const is like a variable, but once created it's value cannot be changed.";
console.log(CANNOT_BE_CHANGED);
console.log("Attempting to change the value of the const would result in an JS error.")

