'use strict'
//Version 2
//We've split this code up. Now there are two functions that are dedicated to handling each task.
//doWork does the work, then calls displayToScreen().

//This code is more complex, but more flexible.  We can reuse displaytoScreen anytime we need to
//output data to the screen.  As our project gets large, code like this becomes more useful.

//This code is no longer dependent on the loader HTML element. If no argurment is provided, it will assume we want
//to use the element with ID loader, but we can pass it any ID we need. It's much more useable.
function displayToScreen(data, displayer="loader" ){
    document.getElementById(displayer).textContent = data;
}

//POTENTIAL PROBLEM: 
//doWork is now TIED TO displayToScreen.  Every time we doWork, it will always call displayToScreen().
//What if we had a more complex need, like sending this data to a web server? 
function doWork(n){
    
    n =  Math.pow(n,3);
   displayToScreen(n) //Always call displayToScreen!

}




