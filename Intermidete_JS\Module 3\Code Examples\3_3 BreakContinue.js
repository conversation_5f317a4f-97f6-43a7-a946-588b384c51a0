"use strict";
const numberOfLoops = 20;

function runLoop()
{





    //Get the values of the input boxes
    let breakOnNumber = document.getElementById("breakon").value;
    let continueOnNumber = document.getElementById("continue-on").value
    
    //JS will treat the input numbers as text.  We need to convert them to numbers.
    breakOnNumber = Number.parseInt(breakOnNumber);
    continueOnNumber = Number.parseInt(continueOnNumber);

    let output = document.getElementById("output");
    
    //Clear the output box .
    output.innerHTML = "";

    for(let i = 0; i < numberOfLoops; i++)
    {

        //if our loop number matches the break number, stop the loop
        if(breakOnNumber === i)
        {
            break;
        }

        //else if our loop counter matches the continue, skip this loop and continue on.
        else if(continueOnNumber === i + 1)
        {
            continue;
   
        }


        //The loop will create an element and append it to the output box.
        let loop = document.createElement("p");
        loop.textContent = `The loop has executed ${i + 1} times.`
        output.append(loop);

    }
}