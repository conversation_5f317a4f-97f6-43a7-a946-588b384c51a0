import { Book } from "./book.js";
import { Patron } from "./patron.js";
import { Address } from "./address.js";

class Library{
    constructor(name){
        this.name = name;
        this.patrons = new Map();
        this.collection = new Map();

        this.generateSampleData();
    }

    //Add a new book to the collection.
    addBook(title, author, year, genre, copies) 
    {
        let book = new Book(title, author, year, genre, copies);
        this.collection.set(book.title, book);
    }

    //Return all books that belong to the collection.
    getBookCollection()
    {
        return this.collection;
    }

    //Return requested book object
    getBook(title)
    {
        let book = this.collection.get(title);
        return book;
    }

    //Adds a new patron to the patrons list.
    addPatron(firstName, lastName, address, birthday, phone, email)
    {
        let p = new Patron(firstName, lastName, address, birthday, phone, email)
        this.patrons.set(p.email, p);
    }

    //Return all patrons of the library
    getAllPatrons()
    {
        return this.patrons;
    }

    //Return a single patron object
    getPatron(email){
        let patron = this.patrons.get(email);
        return patron;
    }

    newAddress(street, city, state, zip){
        let address = new Address(street, city, state, zip);
        return address;
    }

    checkout(title, email)
    {
        if(title === "")
        {
            return;
        }

        let patron = this.patrons.get(email);
        let book = this.collection.get(title);

        if(book.copies >= book.onLoan){
            patron.loan(book);
            book.onLoan++;
        }
    }

    checkIn(title, email){
        if(title === "")
        {
            return;
        }

        let patron = this.patrons.get(email);
        let book = this.collection.get(title);

        patron.checkIn(book);
        book.onLoan--;

    }

    


    //This will generate some test data for us to work with.
    generateSampleData()
    {
        let book = new Book("Game of Thrones", "George RR Martin", 1996, "Fiction", 1);
        this.collection.set(book.title, book);
        book = new Book("A Brief History Of Time", "Stephen Hawking", 1988, "Non Fiction", 2);
        this.collection.set(book.title, book);
        book = new Book("Treasure Island", "Robert Louis Stevenson", 1883, "Fiction", 2);
        this.collection.set(book.title, book);

        let address = new Address("123 Any Street", "Any City", "CA", 12345);
        let patron = new Patron("Sarah", "Marshall", address, "1980-07-12", "123-333-4567", "<EMAIL>");
        this.patrons.set(patron.email, patron);

        address = new Address("555 Main Street", "Any Town,", "NC", 12345) 
        patron = new Patron("Thomas", "Blackburn", address, "1991-02-10", "333-225-2225", "<EMAIL>")
        this.patrons.set(patron.email, patron);
    }



}

export {Library};