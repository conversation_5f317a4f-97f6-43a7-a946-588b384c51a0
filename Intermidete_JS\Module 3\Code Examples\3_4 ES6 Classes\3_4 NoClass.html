<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Object Oriented Programming</title>
</head>
<body>
    <h1>Creating Objects Using Function Based Approached.</h1>

    <p>Prior to JavaScript ES6, released in 2017, there was no keyword "class".  We used a function-based approach to creating 
        complex objects. Using a constructor function,  we can create a blueprint for creating objects.
    </p>
    <p>
        This function-based approach is fairly unique to JS.  Most other languages use the keyword Class for designing objects.
    </p>

    <p>
        In addition to the constructor function, a special property of the object called .prototype allows us define object methods
        that can be passed down to specialized versions of the DataType called children. 
    </p>

    <h2>Guidelines & Best Practices</h2>
    <ol>
        <li>Capitialize the constructor function. "Animal", "Dog", "Person". "Page"</li>
        <li>Define data properties using keyword this.</li>
        <li>Define methods that can not be passed to child types in the constructor. Methods defined this way will be availabe only to objects belonging to the object data type.</li>
        <li>Define methods that can be passed to children in the .prototype property. This occurs outside the constructor function.</li>
    </ol>
    <script src="3_4 NoClass.js"></script>
</body>
</html>