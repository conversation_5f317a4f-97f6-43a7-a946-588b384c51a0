'use strict';

//object literals
let car = {
    make:'Honda',  //property
    model:'Accord', //property
    price: 19_999, //property
    doorsLocked: false, //property
    drive: function() {console.log ("Driving the Car");}  //method
}

//repeating ourselves, not good // DRY principle
let car2 = {
    make:'Ford',
    model:'F150',
    price: 28_799,
    doorsLocked: false,
    drive: function() {console.log ("Driving the Car");}
}

//'factory function' that produces similar objects
//DRY
function createCar(make, model, price, locked)
{
    let car = {
        make: make,
        model: model,
        price: price,
        doorsLocked: locked,
        drive: function() {console.log ("Driving the Car");}
    }

    return car;
}

let car3 = createCar('Volkswagen', 'Jetta', 14_999, false)

//Constructor functions.  Builds Car Objects
//Creates instances of the Car Constructor
function Car(make, model, price, locked){
    this.make = make;
    this.model = model;
    this.price = price;
    this.doorsLocked = locked;
    this.drive = function() {console.log ("Driving the Car");};
}
//new*****
Car.prototype = Object.create(Car.prototype); //Don't worry about this line for now.
//new****

let car4 = new Car('BMW', 'i4', 55_400, false);


//Type Of Keyword
console.log('--typeOf, constructor for a basic string --')
let word = "my word";
console.log(typeof(word));  //string
console.log(word.constructor.name)  //String

console.log('--typeOf, constructor for a basic number --')
let number = 10;
console.log(typeof(number));   //number
console.log(number.constructor.name)  //Number

console.log('--typeOf, constructor for a basic function --')
let fn = function() {console.log("Hello World")};
console.log(typeof(fn));  //function
console.log(fn.constructor.name) //Function


console.log('--The typeof, constructor, and instanceof Car for the car3 object, created via factory function --')
console.log (typeof(car3) )   //Type Of IS "Object"
console.log (car3.constructor.name) //Constructor IS "Object"
console.log (car3 instanceof Car) // false, not a "car object "

console.log('--he typeof, constructor, and instanceof Car car4 object, created via constructor function --')
console.log (typeof(car4) )   //Type Of IS "Object"
console.log (car4.constructor.name) //Constructor IS "Car"
console.log (car4 instanceof Car) //true, is a "car object"

//Does not matter how the object was created. typeof will always return "object"
//what "type" of object is it?
    //type is the constructor that built it. constructor.name, not typeof