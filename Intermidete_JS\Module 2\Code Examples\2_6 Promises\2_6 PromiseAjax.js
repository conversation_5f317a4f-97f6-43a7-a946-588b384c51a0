'use strict';

//AJAX Call with a promise.  Wrapping our AJAX call in a promise, to make it easier to work with.
function makeRequest(url) {
    let promise = new Promise((resolve, reject) => {
        let request = new XMLHttpRequest();
        request.onreadystatechange = function () {

            if (request.readyState == 4) {
                if (request.status == 200) {
                    resolve(request.responseText);
                } 
                else {
                    //we can't call reject prior to getting to ready state 4. This is the reason for the if statement's logic change here.
                    reject(`Bad Request: Ready State: ${request.readyState} HTTP Status:  ${request.status} `);
                }
            }

        }
        request.open("get", url);
        request.send();
    });


    promise.then(success => {

        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = success
    },
    failure => {
        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = failure

    });

}

function makeBadRequest() {
    let url = `https://badrequest`;
    makeRequest(url);
}

function makeGoodRequest() {
    let url = `https://randomuser.me/api/?nat=us`;
    makeRequest(url);
}