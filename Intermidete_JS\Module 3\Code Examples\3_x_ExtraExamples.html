<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="html, training, lessons, templates">
    <meta lang="en-US">
    
    
    <title>JS Lesson 10-6</title>
    <style>

    .threeA, .fourA, .fourB, .five{
      box-sizing: border-box;
      width: 200px;
      height:200px;
      background-color: rgb(200,200,200);
      margin-left: 200px;
      margin-top: 15px;
      margin-bottom: 15px;
      border: 2px solid black;
      font-size: 1.2em;
      overflow:auto;
    }
        
    #btn1{
      width: '75px';
      margin: auto;
      float: right;
      padding: 1%;

    }
    </style>


  </head>


  <body>
    <h1>JS Lesson 10_6</h1>
    <h3> Three A</h3>
      <div class='threeA' id='threeA' onmouseover='changeColorIn()' onmouseout='changeColorOut()'> </div>
    <h3> Three B</h3>
      <p class='threeB' id='threeB'>Starting Text </p>
      <input type='button' value='Change the text.' onClick='changeText()'>
    <h3> Four  A</h3>
      <label for='objectValue1'> Object Value 1 </label>
      <input type='text' id='objectValue1' name='objectValue1'>
      <label for='objectValue2'> Object Value 2 </label>
      <input type='text' id='objectValue2' name='objectValue2'>
      <input type='button' value='Create Object' onClick='createObject()'>
      <div class='fourA' id='fourA'></div>
    <h3> Four  B</h3>
      <label for='pwd1'> Enter Password </label>
      <input type='password' id='pwd1' name='pwd1'>
      <label for='pwd2'> Confirm Password</label>
      <input type='password' id='pwd2' name='pwd2'>
      <input type='button' value='Check Password' id='checkPW' onClick='checkPW()'>
      <div class='fourB' id='fourB'></div>

    <h3> Five </h3>
      <div class='five' id='five'> </div>


    
    <div id='target'> 
    </div>

  
  <script src='3_x_ExtraExamples.js'></script>

  </body>
</html>