'use strict';

function Animal(name){
    this.animalName = name;
}

Animal.prototype.eats = function() {console.log("All Animals can eat.")}

function Fish(name, type){
    Animal.call(this, name);
    this.type = type;
}   

Fish.prototype = Object.create(Animal.prototype); //copy of the blue print, it's not the org
Fish.prototype.swim = function() {console.log("All Fish can Swim")};


let o = {};

function Shark(name){
    Fish.call(this, name, "Shark");
    this.dangerous = true;
}
Shark.prototype = Object.create(Fish.prototype);

