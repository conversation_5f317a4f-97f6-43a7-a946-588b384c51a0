'use strict';
//Async  Await
//Another way for working with promises.

//A basic function we all know and love.
//The script will run this function, then resume.
function myBasicFunction(){
    console.log("<PERSON><PERSON><PERSON> waited for this.");
}

//This function now runs async.  The script will not wait for it to complete but will continue on.
//We "wait for" the promise in the function to be resolved. When it's done, we can finsh the script.
async function asyncWaitFunction(){
    
    let promise = new Promise( (resolve, reject) => {
        resolve("Ran This Async.")
    });

    let result = await promise;

    console.log(result)
}


async function runAsync(){
   return "My Async function";
}
//Script Code Begins Below....


runAsync().then( output => console.log(output));

myBasicFunction();

//Async Await can be coded like a normal function.
asyncWaitFunction();


console.log("Code that comes after Async Function.");
console.log("Code that comes after Async Function.");
console.log("Code that comes after Async Function.");

