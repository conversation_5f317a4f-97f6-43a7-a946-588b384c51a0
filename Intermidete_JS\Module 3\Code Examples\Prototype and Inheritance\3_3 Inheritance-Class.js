'use strict'
//Inheritance with ES6 Classes

//Create A Parent Class
class Employee {

    //Create the constructor & object properties.
    constructor(firstName, lastName, job, payRate){
        this.firstName = firstName;
        this.lastName = lastName;
        this.job = job;
        this.totalPaid = 0;
        this.payRate = payRate;
    }

    //Create the methods.  The methods will automatically be placed on the prototype chain.
    pay(){
        this.totalPaid += this.payRate;
        console.log("Paid Employee");
    }
}

//Create two employees
let e1 = new Employee("<PERSON>", "Fields", "JavaScript Developer", 2000);
let e2 = new Employee ("<PERSON>", "<PERSON>", "Front End Developer", 2000);


//Create a Manager class that IS A Employee (extends Employee)
class Manager extends Employee {
    constructor(firstName, lastName, job, payRate){
    super(firstName, lastName, job, payRate);
    this.employees = new Set();
    }

    //new method that only Manager objects can perform.
    promoteEmployee(employee, newtitle, newPayRate){
        employee.job = newtitle;
        employee.payRate = newPayRate;
    }
}

let manager = new Manager("<PERSON><PERSON>", "<PERSON>", "Senior DevOps Manager", 3000 );

//Can pay the manager, like a regular employee.
manager.pay()

//new manager functionality
manager.employees.add(e1);
manager.employees.add(e2);

console.log(manager);
console.log(manager instanceof Employee ) // results true
console.log(manager instanceof Manager ) //results true
console.log(typeof (Manager)); //results object
