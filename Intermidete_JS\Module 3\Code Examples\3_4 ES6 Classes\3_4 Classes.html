<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classes</title>
</head>
<body>
    <h1>Creating Objects Using Classes Based Approach</h1>

    <p>
        Beginning in JavaScript ES6, we are able to use the class keyword to achieve the same results with more streamlined syntax.
        For any programmer coming from another language such as Java, Python, or C#, this syntax is much more familiar. 
    </p>

    <p>
        The results, when coded properly, are identical, and it's simply a matter of preferrence.
    </p>
    <h2>Guidelines & Best Practices</h2>
    <ol>
        <li>Use Keyword class. Always capitlize the Class Name.  "Animal", "Dog", "Person", "Employee"</li>
        <li>Define data properties in the constructor() function. Use keyword this.</li>
        <li>Define methods that can not be passed to child types in the constructor. Methods defined this way will be availabe only to objects belonging to the object data type.</li>
        <li>Define methods that can be passed to children in the body of the class. </li>
    </ol>
    <script src="3_4 Classes.js"></script>

</body>
</html>