'use strict';

//The Employee Constructor function.
//Creates an object of Employee
//Place the data in the constructor.
function Employee(firstName, lastName, job, payRate){
    this.firstName = firstName;
    this.lastName = lastName;
    this.job = job;
    this.totalPaid = 0;
    this.payRate = payRate;
}

//Place functions that can belong to all future children in the prototype.
Employee.prototype = {
    pay:function(){
        this.totalPaid += this.payRate;
        console.log("Paid Employee");
    }
}

let myEmployee = new Employee("<PERSON>", "<PERSON>", "JavaScript Develoer", 2500);
myEmployee.pay();
myEmployee.pay();
console.log (myEmployee);

//What type of data is myEmployee?  Answer typeof object.
console.log(typeof(myEmployee) );

//Not helpful. We know it's an object. What type of constructor does the object use?
console.log (myEmployee.constructor.name ) ;
//Bingo. It's an Employee Object.

console.log (Object.getPrototypeOf(myEmployee) ) ;
console.log (myEmployee.prototype) ;




//TEST: Is the object an instance of Employee ?  true
console.log (myEmployee instanceof Employee);

function HourlyEmployee(firstName, lastName, job, payRate){

    Employee.call(this, firstName, lastName, job, payRate)

    this.hoursWorked = 0;
    this.pay = function()
    {
        this.totalPaid += this.payRate * this.hoursWorked;
    }

}
HourlyEmployee.prototype = Object.create(Employee.prototype);



let hourly = new HourlyEmployee("Debra", "Jones", "Data Analysis", 35);
hourly.hoursWorked = 10;
console.log(hourly)
hourly.pay()
console.log (hourly.totalPaid);

console.log(hourly instanceof Employee )
console.log(hourly instanceof HourlyEmployee )