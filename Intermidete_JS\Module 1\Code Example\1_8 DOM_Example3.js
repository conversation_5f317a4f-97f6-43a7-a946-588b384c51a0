let object_1 = {
    width:'300px',
    height:'300px',
    backgroundColor:"#173c48",
    textColor:"white",
    text:"Javascript Object 1",
    fontSize:"1.5em",
    textAlign:"center",
    margin: "20px",
    padding: "50px"
}

let div_1 = document.createElement("div");

div_1.style.width = object_1.width;
div_1.style.height = object_1.height;
div_1.style.backgroundColor = object_1.backgroundColor;
div_1.style.color = object_1.textColor;
div_1.style.fontSize = object_1.fontSize;
div_1.style.textAlign = object_1.textAlign;
div_1.textContent = object_1.text;
div_1.style.margin = object_1.margin;
div_1.style.padding = object_1.padding;


document.body.append(div_1);

let p = document.createElement("p");
p.textContent = "Using Javascript, we can add, remove, or change elements on a page."
div_1.append(p);

//The destructive power of changing innerHTML, textContent, innerText
document.getElementById("list").innerHTML = "<p> Updated the inner HTML of the list . Orginial HTML was lost. </p>"
document.getElementById("list-2").textContent = "<p> Updated list-2, but the HTML markup isn't applied. </p>";

//Better method.. Insert new content.

newListItem = document.createElement("li");

newListItem.textContent = "Inserted into List 3";

document.getElementById("list-3").append(newListItem);