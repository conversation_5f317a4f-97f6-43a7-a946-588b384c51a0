<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Reduce and Filter</title>

    <style>
        .grid{
            display: grid;
            grid-template-columns:repeat(5, 1fr) ;
            gap: 10px;
        }
        .item{
            border: 2px solid black;
            padding: 5px;
        }

        .healthbar{
            background-color: darkred;
            color: white;
            text-align: center;

        }

        .exercise{
            background-color: khaki;
            border-bottom: 2px solid black;
            padding: 10px;
        }

        .exercise h3{
            margin: 0;
        }

        .btn{
            border: none;
            padding: 10px;
            background-color: rgb(74, 203, 226);
        }
    </style>


</head>
        <!-- <div>
        </div>
        <div>
        </div> -->


<body>
    <h1>Map Reduce and Filter Assignment</h1>
    <h2>Map, Reduce and Filter Zombies!</h2>
    <p>In this assignment, you'll use map, reduce, and filter to gather the information you need to stop the zombie horde!</p>
    <div id="group-a" class="grid">
        <div class="exercise">
            <h3>Exercise: Weaken Zombies. </h3>
            <p> This group of zombies are much too powerful. Using the power of Javascript map(), 
                reduce the health of all zombies in this group by half.  </p>
            <button class="btn" onclick="weakenZombies()"> Weaken Zombies</button>
        </div>
    </div>
    <div id="group-b" class="grid">
        <div class="exercise"> 
            <h3>Exercise: Calculate Ammo. </h3>
            <p> Your team needs to know how much ammo they need to stop these zombies. One HP point equals one round of ammo. 
                Using the power of Javascript reduce(), determine the total amount of ammo your team needs to destory this group.
            </p>
            <button class="btn" onclick="calculateAmmo()""> Calcualte Ammo</button>
            <p>Ammo Needed: <span id="ammo"></span></p>
        </div>
    </div>
    <div id="group-c" class="grid">
        <div id="exercise-3"class="exercise">
            <h3>Exercise: Find Special Zombie.</h3>
            <p> This group contains a zombie with a property type of "special". This zombie is a critical priorty to capture. 
                Using the power of Javascript filter(), capture the special zombie.
            </p>
            <button class="btn" onclick="findSpecial()" >Find Special Zombie</button>
        </div>
    </div>





    <script src="2_3 MapReduceFilter.js"></script>
</body>
</html>