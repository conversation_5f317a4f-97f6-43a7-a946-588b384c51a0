<!DOCTYPE html>
<html>
  <head>
    <title>HTML DOM Part 3</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
      .flex-row{
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
      }

      .objects-list, .hotspot-group{
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }

      .object{
        background-color: darkgreen;
        color: white;
        min-width: 200px;
        height: 75px;
        text-align: center;
      }

      .hotspot{
        background-color: rgb(50, 65, 50);
        min-width: 200px;
        height: 75px;
        margin: 10px;
        border: 6px solid black;

      }
      
    </style>

  </head>
  <body>
    <h1>HTML DOM Part 3</h1>
    <h3>Drag and Drop Example</h3>
    
    <div class="flex-row">
      <div class="objects-list">
        <div id="drag-item-1" class="object " draggable="true" ondragstart="beginDrag(event)"> Drag Item 1 </div>
        <div id="drag-item-2" class="object " draggable="true" ondragstart="beginDrag(event)"> Drag Item 2 </div>
        <div id="drag-item-3" class="object " draggable="true" ondragstart="beginDrag(event)"> Drag Item 3 </div>
        <div id="drag-item-4" class="object " draggable="true" ondragstart="beginDrag(event)"> Drag Item 4</div>
      </div>

      <div class="hotspot-group">
        <div id="hotspot-1" class="hotspot" ondragover="onDragOver(event)" ondrop="drop(event)"></div>
        <div id="hotspot-2" class="hotspot" ondragover="onDragOver(event)" ondrop="drop(event)"></div>
        <div id="hotspot-3" class="hotspot" ondragover="onDragOver(event)" ondrop="drop(event)"></div>
        <div id="hotspot-4" class="hotspot" ondragover="onDragOver(event)" ondrop="drop(event)"></div>
      </div>
    </div>

    <script src="3_7 DOM-DragAndDrop.js"></script>

  </body>
</html>