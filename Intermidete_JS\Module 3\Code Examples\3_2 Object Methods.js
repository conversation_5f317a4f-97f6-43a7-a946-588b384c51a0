"use strict";

//There is no HTML file for this code example.  This lesson is all about understanding how the code is structured.


//EXAMPLE 1:
//a simple example.  the bark function "belongs" to the myDog object. bark() is a method of myDog. 
//assigning a function to an object (as a method) makes sense when we want the object to "own" the function. 
let myDog = {
  name:"Router",
  age: 4,
  breed: "Black Lab",
  bark: function(){
    console.log(`${this.name} says bark!`);
    //for the bark function to access the 'name' property of the myDog object, we must write this.name
    //we'll discuss this more later.
  }
}

//It is called by writing myDog.bark()
myDog.bark();



//EXAMPLE 2:
//JS is very flexible.  We can create a "standalone function".
function basicfunc(){
  console.log("I'm just a basic function");
}

//Calling the basic function
basicfunc(); 

//Then we assign the function to a property of a object.  It will be both a standalone function as well as a method of the object.
let someObject = {
  //The object property called "prop" has been assigned the "basicfunc" function. It's now a method of the someObject.
  prop: basicfunc
}

//When we execute the method someObject.prop(), the basic function is called.
someObject.prop();


//Many things in JS have built in methods. When we create an array, it has built in methods such as push() 
let myArray = [1,2,3];
myArray.push(4);  //built in push method of all arrays
myArray.length; //lenght property of all arrays --> built in.


let myWord = "Hello";
myWord.slice(0,2);  //bult in slice method of all string

document.getElementById("someid");  //build in method of the document object

Math.random() // build random method of the Math object.

parseInt("300px"); // built in function of JS
isNaN("not a number");  //built in function of JS


