<!DOCTYPE html>
<html>
  <head>

    <style>

      .content{
        background-color: #ccc;
        border: 2px solid black;
        margin: auto;
        padding: 10px;
        width: 50%;
      }

      .red-italic{
        color: red;
        font-style: italic;
        font-size: 1.3em;
      }

      .list-style{
        font-size: 1.2em;
        color: green;
      }

      .simple-class{
        background-color: #eee;
        width: 25%;
        margin: 20px;
        border: 2px solid black;
        text-align: center;
      }

      .flex-row{
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
      }

      .flex-item {
        background-color: orange;
        color: darkblue;
        font-size: 1.5em;
        border: 3px solid darkblue;
        margin: 10px;
        padding: 10px;
      }

      .removal-class{
        color:darkmagenta !important;
      }

    </style>
  </head>
  <body>

    <h1>JS - Working with Multiple Elements</h1>

    <h3 class="info"> Getting elements by Tag </h3>
    <p>Paragraph content </p>
    <p>Paragraph content</p>
    <p>Paragraph content</p>
    <p>Paragraph content</p>

    <h3 class="info"> Getting a single element, then accessing it's children </h3>
    <ul id="ul-1">
      <li>List item 1</li>
      <li>List item 2</li>
      <li>List item 3</li>
      <li>List item 4</li>
    </ul>

    <h3 class="info"> Getting elements by Class Name </h3>
    <div class="simple-class">
      Simple Class
    </div>

    <div class="simple-class">
      Simple Class
    </div>

    <div class="simple-class">
      Simple Class
    </div>

    <h3 class="info"> Getting elements by a CSS Rule </h3>
    <div id="selector-example">
      <div>
        <p>Content</p>
        <p>Content</p>
        <p>Content</p>
      </div>
    </div>

    <h3>Getting a single element, working with it's children, additional example.</h3>
    <div class="flex-row" id="flex-parent">
      <div class="removal-class">Child Item A</div>
      <div class="removal-class">Child Item B</div>
      <div class="removal-class">Child Item C</div>
      <div class="removal-class">Child Item D</div>
      <div class="removal-class">Child Item E</div>
      <div class="removal-class">Child Item F</div>
    </div>

    <script src="2_6_DOM-MultipleElements1.js"></script>
  </body>
</html>