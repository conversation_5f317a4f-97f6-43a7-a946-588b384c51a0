//A class created in this file.
class Animal{
    constructor(animalName, age, color, isFriendly)
    {
        this.animalName = animalName;
        this.age = age;
        this.color = color;
        this.isFriendly = isFriendly;
    }

    eat(){
        console.log("The Animal eats some food.")
    }

    sleep()
    {
        console.log("The Animal goes to sleep.")
    }
}

//A helpful function to check if an object is an instance of the Animal Class
function isAnimal(object){
    return object instanceof Animal
}

//Export content form this file, that can be used anywhere.
export {Animal, isAnimal};