<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promise and Timeouts</title>
    <style>

        .flexbox{
            display: flex;
            margin: 10px;
        }

        #output{

            background-color: rgb(230, 212, 187);
            min-height: 100%;
            flex-grow: 1;
            text-align: center;
            font-size: 3em;
            border: 5px solid black;

        }

        .coinwindow{
            width: 450px;
            padding: 10px;
            border: 5px solid black;
        }


        .btn{
            padding: 1em;
            text-align: center;
            background-color: green;
            color: white;
            border: none;
            margin: 10px;

        }
		
		.coinflip{
            animation-name: flip;
            animation-duration: 1s;
            
        }

        @keyframes flip{
            0%{transform: rotateX(0);}
            100%{transform: rotateX(360deg);
                }
        }
    </style>

</head>
<body>
    <h1>Promise and Timeouts</h1>
    <p>In this assignment, you'll use JavaScript Promises and setTimeout to create a coin flipping application.</p>

    <div class="flexbox">
        <div class="coinwindow">
            <img src="coin.svg" alt="coin" id="coin" >
        </div>
        <div id="output"></div>
    </div>


    <button class="btn" onclick="flip()">Coin Flip</button>

    <script src="Assignment 2_2 PromiseTimeout.js"></script>
</body>
</html>