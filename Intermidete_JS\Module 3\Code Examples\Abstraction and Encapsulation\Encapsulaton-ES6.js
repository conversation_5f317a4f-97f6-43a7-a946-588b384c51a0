//In ES5, using a closure was the only way to protect a property and make it private.
//We can use a closure in the constructor to achieve the same result in ES6
//However, this method does not work as intended when the functions are moved outside of the constructor.
//This method does not work with get and set.
class Fruit{
    constructor(descrip){
        var _price;
        this.description = descrip;
        this.getPrice = function(){ return _price};
        this.setPrice = function(value) {
            if(value >= 0)
            {
                _price = value;
            }
            else{
                return "New Price must be greater than zero";
            }
         }
    }
}

//In ES6, we can declare a property as private with the # sign. 
//However, not all browsers support this feature yet.
//With this version, we can move our methods out of the constructor, which is better.

//If this code doesn't work on your device, your browser most likely doesn't support this syntax yet.
class ProtectedFruit{
    #price;
    constructor(descrip){
        this.description = descrip;
    }

    getPrice() { return this.#price};
    setPrice(value) {
        if(value >= 0)
        {
            this.#price = value;
        }
        else{
            return "New Price must be greater than zero";
        }
     }

}
//We can also choose to use get and set, if we prefer that option.
class GetSetExample{
    #price; //indicate the #price is private. Is not publicly accessible. 
    
    constructor(descrip){
        this.description = descrip;
    }

    //implement price, the public interface to get and set private #price.
    get price() { return this.#price};
    set price(value) {
        if(value > 0)
        {
            this.#price = value;
        }
        else{
            throw "New Price must be greater than zero";
        }
     }

}