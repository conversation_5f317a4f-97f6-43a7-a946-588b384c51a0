<!DOCTYPE html>
<html>

<head>
  <title>
    JS Object and JSON
  </title>

  <style>
    div{
      margin: 10px;;
    }
  </style>

</head>

<body>
  <h1>JS Object To JSON Export</h1>
  <p>You can export a JS Object to JSON formatted text.</p>
  <p>Use the form below to create book objects. Clicking the button will create the object & provide you with an JSON
    export of book</p>

  <div>
    <div>
      <label for="title">Book Title</label>
      <input type="text" id="title">
    </div>
    <div>
      <label for="title">Book Author</label>
      <input type="text" id="author">
    </div>
    <div>
      <label for="title">Year Published</label>
      <input type="number" id="year" min="1600" max="2022">
    </div>
    <div>
      <label for="genre">Genre </label>
      <select name="genre" id="genre">
        <option value="Fiction">Fiction</option>
        <option value="Non Ficton">Non Fiction</option>
        <option value="Sci/Fi Fantasy">Sci/Fi Fantasy</option>
        <option value="Classics">Classics</option>
        <option value="Politics">Politics</option>
      </select>
    </div>


  </div>
  <button onclick="createBook()">Create New Book</button>

  <div>
    <p>JSON Text:</p>
    <pre id="jsonText">

    </pre>
  </div>


  <script src="1_6 JSON Data.js"></script>
</body>

</html>