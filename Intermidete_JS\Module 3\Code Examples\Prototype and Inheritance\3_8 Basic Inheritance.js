'use strict';
//Example of  Inheritance
function Animal(name, arms = 2, legs = 2, dangerous = false, furry = false){
    this.name = name;
    this.arms = arms;
    this.legs = legs;
    this.isDangerous = dangerous;
    this.isFurry = furry;
    //No methods in the construction function!
}
Animal.prototype.eats =   function(){return this.name + " eats some food." };
Animal.prototype.sleep = function() {return this.name + " goes to sleep" }; 


let bobo = new Animal("<PERSON><PERSON> the Monkey", 2, 2, false, true);
let friendly = new Animal("Friendly the Spider", 0, 8, false, false)
let crazyCat = new Animal("Crazy Cat", 0, 4, true, true);

console.log(bobo);
console.log(friendly);
console.log(crazyCat);
console.log ( bobo.eats() );


function Dog(name, dangerous = false, favoriteToy="Chew Toy" ){
    Animal.call(this, name, 0, 2, dangerous, true); //All of the properties of the Animal Object are added here.
    this.favoriteToy = favoriteToy; //Someting new
}

//Dog.prototype = Animal.prototype; //WRONG.  While this works, it means all animals can bark when we add bark to the same chain.
Dog.prototype = Object.create(Animal.prototype); //CORRECT. Make a clean copy of the chain.
Dog.prototype.bark = function() {return this.name + " goes BARK!"};


//The issue here is that we repeat ourselves with the Dog class. We want a way to borrow from the Animal class
//We also want the answer to the question is a Dog an Animal to be true. Current a dog is not an animal.

let myDog = new Dog("Lucky", false, "Tennis Ball");
console.log(myDog);
console.log (myDog instanceof Animal) //Is a Dog an Animal?  This code says YES.
console.log (myDog.eats() );
console.log (myDog.bark() );

//console.log(bobo.bark())
//Bobo can not bark because he is an Animal and only Dogs can bark.