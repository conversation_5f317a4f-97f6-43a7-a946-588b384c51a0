'use strict';
//A basic function, not a generator
function getNegativeOne(){
    return -1;
}

//Generators
function*  generate(){

    yield 1;
    yield 2;
    yield 10;
    return 5;
}

const myGenerator = generate();

let x = myGenerator.next();
let y = myGenerator.next();
let z = myGenerator.next();
let zz = myGenerator.next();
let done = myGenerator.next();

console.log (x , y, z, zz, done);


function* story(){
    yield "Once upon a time,"
    yield "There were three bears,"
    yield "who lived together in a house,"
    return "in the woods."
}


const threeBears = story();

for(let i=0; i < 4; i++){
    console.log ( threeBears.next() );
}

//Generator loop control
function* myLoop(n){
    for(let i = 0; i < n; i++)
    {
        yield `The loop as ran ${i+1} times.`
    }

}


const runLoop = myLoop(4);
console.log (runLoop.next() );
console.log (runLoop.next() );
console.log (runLoop.next() );
console.log (runLoop.next() );
console.log (runLoop.next() );

