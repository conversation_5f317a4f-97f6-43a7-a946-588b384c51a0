<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=<device-width>, initial-scale=1.0">
    <title>Class Based Inheritance</title>
</head>
<body>
    <h1>Inheritance Using Class Based Approach.</h1>
    <p>When using the class based approach, we can establish the parent \ child relationship using the keyword extends</p>
    <p>If the constructor is  the same, there is no need to setup a new constructor function.</p>
    <p>When overriding the constructor, you will need to use keyword super to execute the parent\super constructor before 
        customing it further with new properties. </p>
    <script src="3_3 Inheritance-Class.js"></script>
</body>
</html>