<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="html, training, lessons, templates">
    <meta lang="en-US">
    
    <title>JS <PERSON>ie</title>
    <style>

    .flex-row{
      display: flex;
      flex-direction: row;
    }

    .flex-col{
      display: flex;
      flex-direction: column;
    }

    #target{
      box-sizing: border-box;
      width: 500px;
      height:500px;
      background-color: rgb(200,200,200);
      margin:  0 20px;
      border: 2px solid black;
      font-size: 1.3em;
      padding: 1rem;
      
    }

    #btn1, #btn2{
      min-width: 75px;
      padding: 10px;
      margin: 10px;

    }

    .tips{
      max-width: 300px;
      background-color: khaki;
      padding: 1rem;
    }

    .visitor-notice{
      background-color: darkgreen;
      padding: 1rem;
      color: white;
      font-size: 1.1em;
    }


    </style>


  </head>


  <body>
    <h1>JS Cookie and Web Storage</h1>

    <section class="flex-row">
      <div class="flex-col">
        <input type='button' id="btn1" onclick="jsWebStorage()" value='Add to Web Storage JS'>
        <input type='button' id="btn2" onclick="clearWebStorage()" value='Clear Web Storage and Cookies for this site'>
      </div>
    <div id='target'> 

    </div>

    <div class="tips">
      <p>Remember, data put in Local Storage will remain there, even after the browser is closed. This is long term storage. </p>
      <p>Data put in Session storage will remain there until the browser is closed. This is for short term storage, for the user's current session.</p>
      <p>Cookies, by default, will be removed when the browser is closed. However, you can place an expiry date on the cookie, and the cookie will live until it expires.</p>
    </div>
  </section>


  
  <script>
    "use strict";
    function jsWebStorage(){
      sessionStorage.setItem("lastname","Smith")
      sessionStorage.setItem("firstname","Sam") 
      sessionStorage.setItem("optin","True") 
      sessionStorage.setItem("favoriteProduct","Sports Equipment") 

      var element = document.getElementById("target");

      let infoText1 = document.createElement("p");
      let infoText2  = document.createElement("p");

      infoText1.textContent = "We can not show an example of cookies without a web server and HTTP protocol. " +  
      "Chrome does not allow cookies to be created with JS without HTTP protocol. If you are using Visual Studio Code " +
      "Live Server this won't be an issue. But if your browser address bar says file://, your browser will reject these cookies. ";

      infoText2.textContent = "Open the Chrome Dev Tool > Application to view Session Storage, Local Storage, and Cookies.";

      element.append(infoText1);
      element.append(infoText2);
    }

    function clearWebStorage() {
      localStorage.clear();
      sessionStorage.clear();
    }

    let hasVisited = localStorage.getItem("lastTimeVisited");     
    console.log(hasVisited);
    let welcome = document.createElement("p");
    welcome.className = "visitor-notice";


    if(hasVisited == null){
      localStorage.setItem("lastTimeVisited", new Date());
      welcome.textContent  = `Welcome, new first-time visitor!`;

    }
    else{
      lastVisit = new Date(hasVisited);
      welcome.textContent  = `Welcome back. The last time you visited this page was ${lastVisit}`;
      localStorage.setItem("lastTimeVisited", new Date());
    }

    document.body.append(welcome);

    //Cookies
    document.cookie = "newCookie=JavaScript is Awesome!";
    document.cookie = "userName=superCoder11";
    let expireDate  = new Date("12/1/2031").toGMTString();  //format that the cookie expects the date in.
    document.cookie = `autoPlayVideos=false; expires=${expireDate}`;



  </script>

  </body>
</html>