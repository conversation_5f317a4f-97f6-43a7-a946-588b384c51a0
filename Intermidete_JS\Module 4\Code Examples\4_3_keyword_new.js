"use strict";

//The simplist way to create an object. The object literal
let someColor = "red";

let myObject = {
  width: '100px',
  height: '250px',
  color: someColor,
  text: 'white'
}

console.log(myObject);

//the new Object()
let anotherObject = new Object();
anotherObject.width = '400px';
anotherObject.height = '400px';
anotherObject.color = "blue";
anotherObject.text = "white";

console.log(anotherObject);



//Create an generic object, without using new
//function based approach
function createEmployee(name, jobTitle)
{
  let employee = {
    name: name,
    jobTitle: jobTitle
  }

  return employee;
}

let myEmployee = createEmployee("<PERSON>", " General Manager");
//My employee is a generic object.

//constructor approach
//we can use the new keyword!
function Employee(name, jobTitle)
{
  this.name = name;
  this.jobTitle = jobTitle;
}

let realEmployee = new Employee("<PERSON>", "Sales Manager")
//real employee is an object of data type Employee
console.log(typeof realEmployee);

//Car Example
function Car(make, model, color, price)
{
  this.make = make;
  this.model = model;
  this.color = color;
  this.price = price;
  this.drive = function(){
    console.log(`You drive the ${this.make} ${this.model}`);
  }
}

let honda = new Car("Honda","Civic","Yellow",12_999);
let ford = new Car("Ford", "Focus", "Silver",14_750 );

console.log (honda.drive()  );
console.log (ford.drive()  );

//Person example
const Person = function(firstName, lastName, age, job) {
  this.firstName = firstName;
  this.lastName = lastName;
  this.age = age;
  this.job = job;
  this.about = ()=> {return  `My name is ${this.firstName} ${this.lastName} and my job is a ${this.job}`}
}

let kayla = new Person("Kayla","Morris", 30, "JavaScript Web Developer");
let danny = new Person("Danny","Simpson",28, "Backend Web Developer");


//formal class approach
class Movie{
  constructor(title, movieStar) {
    this.title = title,
    this.movieStar = movieStar 
  }
}

let badMovie = new Movie("The Room", "Tommy Wiseau")
console.log(typeof badMovie);
console.log(badMovie);





//DOM Output
let firstDOMObject = document.createElement("div");
let secondDOMObject = document.createElement("div");

firstDOMObject.style.width = myObject.width;
firstDOMObject.style.height = myObject.height
firstDOMObject.style.backgroundColor = myObject.color;
firstDOMObject.style.color = myObject.text;
firstDOMObject.textContent = "Created from myObject data."
document.body.append(firstDOMObject);

secondDOMObject.style.width = anotherObject.width;
secondDOMObject.style.height = anotherObject.height
secondDOMObject.style.backgroundColor = anotherObject.color;
secondDOMObject.style.color = anotherObject.text;
secondDOMObject.textContent = "Created from anotherObject data."
document.body.append(secondDOMObject);


let employeDOMObject = document.createElement("ul");
document.body.append(employeDOMObject);

for(let prop in realEmployee)
{
  let li = document.createElement("li");
  li.innerText = `${prop} : ${realEmployee[prop]}`;
  employeDOMObject.append(li);

}