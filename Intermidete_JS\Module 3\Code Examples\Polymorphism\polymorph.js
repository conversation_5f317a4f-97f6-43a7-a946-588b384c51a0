'use strict';
//PolyMorpism Example
//Create A Parent
function Parent(name){
    this.name = name;
}
//Create Parent Methods on the prototype chain
Parent.prototype.doWork = function() {
    console.log("Do Some Work");
}

//Create a Child, inhert parent properties
function Child(name){
    Parent.call(this, name);

}
//Inhert a new copy of the prototype chain
Child.prototype = Object.call(Parent.prototype);


//Polymorphism
//Override the doWork method on the chain.
Child.prototype.doWork = function() {
    Parent.prototype.doWork();
    console.log("Do Something Else");
}

//The parent will doWork like normal.
let parent = new Parent("parent");
console.log(parent);
parent.doWork();


//The child will doWork with different outcome fro the Parent.
let child = new Child("child");
console.log(child)
child.doWork();
