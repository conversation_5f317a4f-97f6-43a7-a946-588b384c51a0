"use strict";
//In the haveAGreatDay function, the required arguements are a person and a birthday.
//We can not use the function unless we provide this input
function haveAGreatDay(person, day)
{
  console.log(`Hello ${person}. Today is ${day}. I Hope it's a great one!`);
}

//Now That The function has been defined, we can use it by calling it.


haveAGreatDay("Johnthan", "the best day");
haveAGreatDay("Morgan", "a lovely day");
haveAGreatDay("Simer", "a new day");


haveAGreatDay(); //No arguements provided


haveAGreatDay('<PERSON>', 'Christmas Day', 'More input is ignored'); //too many arguements provided, extra
//input is ignored.

//Supplying a default arguement. This will be used if the arguement is not provided with the function
//is called
function betterHaveAGreatDay(person="stranger", day="a new day"){
  console.log(`Hello ${person}. Today is ${day}. I Hope it's a great one!`);
}

console.log(betterHaveAGreatDay())
console.log(betterHaveAGreatDay("Justin","your day!"))

//Returning Data
function doesNotReturnData(number1, number2)
{
  let result = number1 + number2;
  console.log(`The result is ${result}`);
}

function returnSomeData(number1, number2)
{
  let result = number1 + number2;
  console.log(`The result is ${result}`);
  return result;
}

//Console Output is not the same thing as returning Data
//Both functions print console output
doesNotReturnData(5, 5);
returnSomeData(8,8);

//...but only one returns data so that we can work with it.
let whatDataWasReturned = doesNotReturnData(5, 5);
console.log(whatDataWasReturned);

whatDataWasReturned = returnSomeData(8,8);
console.log(whatDataWasReturned);

function doubleANumber(number)
{
  return number * 2;
}

let newNumber = doubleANumber(whatDataWasReturned);
console.log(newNumber);


//DOM EXAMPLE
function readHTMLTable(){
  let tableData = document.getElementById("table-data");
  console.log(tableData);
  for(let row of tableData.children)
  {
    //if this is the first row with table headers, skip it.
    if(row == tableData.children[0])
    {
      continue;
    }

    let person = []
    for(let col of row.children)
    {
      person.push(col.innerText);
    }

    wishHappyBirthday(person[0], person[1])
  }
}

function wishHappyBirthday(person, age)
{
  let birthdayWish = `\u{1F973} Happy Birthday ${person} ! You turned ${age} today! \u{1F382}`;

  let element = document.createElement("div");
  element.innerText = birthdayWish;
  element.classList.add("birthday-card")
  document.body.append(element);

}

readHTMLTable();