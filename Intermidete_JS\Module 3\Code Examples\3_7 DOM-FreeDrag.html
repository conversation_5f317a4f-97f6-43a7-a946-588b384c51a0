<!DOCTYPE html>
<html>
  <head>
    <title>HTML DOM Part 3</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>


      .object{
        background-color: darkgreen;
        color: white;
        min-width: 200px;
        height: 75px;
        text-align: center;
        position: absolute;
      }


      
    </style>

  </head>
  <body>
    <h1>HTML DOM Part 3</h1>
    <h3>Free Drag Example</h3>

    <div id="drag-item-1" class="object " onmousedown="beginDrag(this, event)" onmousemove="onDrag(this, event)" onmouseup="release(this, event)" > Free Drag</div>

    <script src="3_7 DOM-FreeDrag.js"></script>

  </body>
</html>