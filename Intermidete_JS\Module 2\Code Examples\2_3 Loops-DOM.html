<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="html, training, lessons, templates">
    <meta lang="en-US">
    
    <title>JS Loop</title>
    <style>
    div{
      display:flex;
      box-sizing: border-box;
      width: 500px;
      height:500px;
      background-color: rgb(200,200,200);
      margin: auto;
      border: 2px solid black;
      font-size: 1em;
      overflow: scroll;
      
    }


    #btn1{
      width: '75px';
      margin: auto;
      padding: 10px;

    }
    </style>


  </head>


  <body>
    <h1>JS Loop DOM Example</h1>
    <label for='inputbox1'>How Many Loops?</label>
    <input type=number id='inputbox1' name='inputbox1' value="1" min="1" max="30">
    <input type='button' id="btn1" onclick="jsLoop()" value='Run Loop'  >


    <div id='target'> 
    </div>

  
  <script>
    function jsLoop(){
      var input1 = document.getElementById('inputbox1');
      var numberOfLoops = Number(input1.value) ;

      var results = [];

      for (var i= 0; i < numberOfLoops; i++ ){
        results[i] = `loop has ran ${i+1} times \n`;
      }

      var element = document.getElementById("target");
      let output = document.createElement("pre");
      output.textContent = results;
      element.append(output);

    }




  </script>

  </body>
</html>