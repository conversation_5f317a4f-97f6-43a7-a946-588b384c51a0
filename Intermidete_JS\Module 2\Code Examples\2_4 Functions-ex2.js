"use strict";
//functions Example2
//this is the alterate example showing highlighting how arguements work.

let y = 3
let x = 2

//thisFunction works the same, even when we name the arguements jelly and peanutButter
function thisFunction(jelly,peanutButter){
     let result = jelly+peanutButter
     return result
}

let thisVar = thisFunction(10,10)
console.log(thisVar)

let thatVar = thisFunction(x,y)
console.log(thatVar)