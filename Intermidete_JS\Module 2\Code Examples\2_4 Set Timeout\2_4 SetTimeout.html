<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Timeout</title>

    <style>
        div{
            margin: 10px;
        }
    </style>
</head>

<body>

        <h1>Set Timeout Examples</h1>
    <div>
        <button onclick="executeNormal()"> Run Normal</button>
        <button onclick="executeAsync()"> Run Async (Set TimeOut)</button>
        <button onclick="startOven()">Start Oven</button>
    </div>

    <div>
        <label for="arrivaltime">Total time for Taxi to Arrive (In Seconds) </label>
        <input id="arrivaltime" type="number" min=1 max=10>
    </div>

    <div>
        <label for="maxwaittime">Max time you are willing to Wait (In Seconds)</label>
        <input id="maxwaittime" type="number" min=1 max=10>
    </div>
    <div>
        <button onclick="taxi()">Get Taxi</button>

    </div>


    <script src="2_4 SetTimeout.js"></script>
</body>

</html>