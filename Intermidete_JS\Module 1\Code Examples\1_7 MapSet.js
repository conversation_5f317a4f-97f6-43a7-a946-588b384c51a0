"use strict";
//MAP and SET

//MAP is something like a cross between an object and array in JS.  It has some properties of both.
//The two biggest differences of MAP vs a basic object are
/*
1. It's iterable, so we can loop through it with a for..of loop!
2. It has a size property, like an array!
3. It's keys can be anything, including functions, other objects, strings, numbers, ANYTHING!
4. It takes additional work to export MAP to JSON.  We can do it, but not without writting extra code.
*/

//Health Information
let health = new Map();
health.set("weight", 185);
health.set("age", 45);
health.set("bloodPressure", "normal");


console.log(health);


//Character Stats
let stats = new Map();
//We can begin adding key/values with .set
stats.set('str',5);
stats.set('int',7);
stats.set('spd',4);

//We can get values with .get(key);
let strength = stats.get('str');
console.log(`Character Strength: ${strength}`);

//How big is our map?
console.log (`Size is : ${stats.size}` );


//Loop it with for..of, we can't do this with an regular object!
for(let value of stats){
    console.log(value);
}

//Clear the map of all entries
stats.clear();

//can convert map objects to new objects using Object.fromEntries
let myObject = Object.fromEntries (stats);
console.log( `Object created from the Map ${myObject} `)

//We can convert objects to map objects
let someObject = {name: "Awesome Object", data: 5, };
let myMap = new Map(Object.entries(someObject));
console.log(myMap);

//SET
//SET is similar to an array that only allows for unqiue values.

let unique = new Set();
unique.add('abc');
unique.add('xyz');
//no error, it just doesn't do it.
unique.add('abc');

unique.forEach( (value, value2, unique) => {console.log(`${value} , ${value2}, ${unique} `) } );


