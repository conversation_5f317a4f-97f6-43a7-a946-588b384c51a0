'use strict';
//FETCH
//Does all the work for us.
function makeGoodRequest() {
    let url = `https://randomuser.me/api/?nat=us`;

    fetch(url)  //Fetch the Data from the web server
    .then(response => response.json()) // THEN convert the response to a Js Object with .json()
    .then(object => {  //THEN print the object to the console. Converthe object to pure JSON. Display to page.
        console.log(object);
    
        document.body.append(document.createElement("h1"));
        document.body.lastElementChild.innerHTML = object.results[0].name.first + ' ' + object.results[0].name.last;

        document.body.append(document.createElement("p"));
        document.body.lastElementChild.innerHTML = object.results[0].name.first + ' ' + object.results[0].email;

        document.body.append(document.createElement("img"));
        document.body.lastElementChild.src = object.results[0].picture.medium;

        }

    )





}