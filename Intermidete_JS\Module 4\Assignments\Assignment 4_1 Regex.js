'use strict';
const errorHTML = document.getElementById("errors");
const submit = document.getElementById("submit");


const lowerCase = new RegExp("[a-z]");
const upperCase = new RegExp("[A-Z]");
const number = new RegExp("[0-9]");
const special = new RegExp("[!@#$%^&*()]")
let valid =  false;


const validatePassword = (password) => {
    let passwordTest = true;
    clearError();
    validateConfirm();
    if(!password.match(lowerCase) ){
        passwordTest = false;
        displayError("Password must contain 1 lowercase letter");
    }

    if(!password.match(upperCase) ){
        passwordTest = false;
        displayError("Password must contain 1 uppercase letter");
    }

    if(!password.match(number) ){
        passwordTest = false;
        displayError("Password must contain 1 number");
    }

    if(!password.match(special) ){
        passwordTest = false;
        displayError("Password must contain 1 special character");
   }

    if(password.length < 8){
        passwordTest = false;
        displayError("Password must be at least 8 characters");
    }
    valid = passwordTest;

};

const validateConfirm = (secondPassword) =>{
    if(!valid){
        return;
    }

    let firstPassword = document.getElementById("password").value;
    if(firstPassword != secondPassword){
        displayError("Password Confirm does not match");
    }
    else{
        clearError();
        submit.className = "btn";
        submit.disabled = false;
    }
};

const displayError = (msg) =>{
    let error = document.createElement("li");
    error.textContent = msg;
    errorHTML.append(error);
    submit.className = "btn disable";
    submit.disabled = true;
};


const clearError = () =>{
    errorHTML.innerHTML = "";

};

submit.classList.add("disable");
submit.disabled = true;