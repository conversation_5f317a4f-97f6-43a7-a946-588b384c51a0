<!DOCTYPE html>
<html>
  <head>
    <style>
      body{
        font-size: 1.5em;
      }

      .info{
        background-color: #aaa;
        padding: 1em;
      }

      #flex-row{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      #flex-row div{
        padding: 10px;
        background-color: #6fa6dd;
        margin: 5px;
      }
      .birthday-card{
        font-size: 2em;
        padding: 20px;
        margin: 20px;
        background-color: #823396;
        color: #f9fd00;
      }
      table, th ,td{
        border-collapse: collapse;
        border: 2px solid black;
      }
     th, td{
        padding: 20px;
        text-align: left;
      }




    </style>
  </head>
  <body>
    <h1>JS Functions</h1>

    <p class="info">The output shown below was created by JavaScript. The table is INPUT, written in the HTML.
      A function reads the HTML table, and passes the data on the page to a function
      that wishes the person happy birthday.   Open the Browser Inspector and goto the Console to view 
      more loop examples printed to Console.
    </p>

    <p class="info">
      Modify the HTML table data, and you'll see the JS output will change when you reload the page.
    </p>

    <h2> HTML Input: Today's Birthday Table</h2>
    <table>
      <tbody id="table-data">
        <tr>
          <th>Person</th>
          <th>Age</th>
        </tr>
        <tr>
          <td>Sam</td>
          <td>24</td>
        </tr>
        <tr>
          <td>Marcus</td>
          <td>32</td>
        </tr>
        <tr>
          <td>Aliyah</td>
          <td>18</td>
        </rotrw>
        <tr>
          <td>Liu</td>
          <td>48</td>
        </tr>
        <tr>
          <td>Jonathan</td>
          <td>12</td>
        </tr>
      </tbody>
    </table>

    <h2> JS Output</h2>
    
    <script src="2_4 functions.js"></script>
  </body>
</html>

