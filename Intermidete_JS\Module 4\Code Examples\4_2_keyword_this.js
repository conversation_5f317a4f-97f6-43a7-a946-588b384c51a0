let person = {
  firstName: "<PERSON>",
  lastName: "<PERSON>",
  age: 34,
  job: "Web Designer",
  change<PERSON>ob(newJob) {
    this.job = newJob;
    console.log( `${this.firstName} ${this.lastName} job was changed to ${this.job}.` );
    return true;
  },
  about() {return  `My name is ${this.firstName} ${this.lastName} and my job is a ${this.job}`}
}

person.changeJob("Full Stack Engineer");
console.log(person.about() );


/*
DO:
  When writing inside the body of an object {code block}, refer to it with keyword this.
    this.job = newJob
    CORRECT

DON'T
  When writing inside the body of an object {code block}, Don't reference the object with it's name.
    person.job = newJob  
    LEADS TO TROUBLE DOWN THE ROAD
  Don't only write the property name
   job = newJob      
   WILL CAUSE ERROR! 

*/


//global object
//window object represents the browser window itself
console.log (this.innerHeight) ;


function playThisSong()
{
  console.log(this.song);
  //JS you must be talking abou the window object!
  //window.song  
}


playThisSong();
//returns undefined



let rollingStones = {
  band: "The Rolling Stones",
  song: "Gimme Shelter",
  play: playThisSong
}

let dojaCat = {
  band: "Doja Cat",
  song: "Need to Know",
  play: playThisSong
}

rollingStones.play();
dojaCat.play();



//keyword this and the lambda ()=> function
//the about() method uses a function(). Works as expected.
let employee1 = {
  firstName: "Kayla",
  lastName: "Morris",
  age: 30,
  job: "JavaScript Web Developer",
  about: function() {return  `My name is ${this.firstName} ${this.lastName} and my job is a ${this.job}`}
}

//the about() method uses a lambda ()=> and the meaning of keyword this is lost.
let employee2 = {
  firstName: "Danny",
  lastName: "Simpson",
  age: 28,
  job: "Backend Web Designer",
  about: ()=> {return  `My name is ${this.firstName} ${this.lastName} and my job is a ${this.job}`}
}

 console.log(employee1.about());
 console.log(employee2.about());

 //lambda and this, example #2.
 //It's important to keep in mind that lambda does not have access to the this keyword.
 let simpleObject = {
   someData:100,
   someAction: ()=> {console.log(this.someData); }
 }
 //The problem is that when we call simpleObject.someAction(), it will returned undefined. It can't get the object's someData property through this.
simpleObject.someAction();

 //But everything works great when we don't use the lambda
 let anotherObject = {
  someData:100,
  someAction: function()  {console.log(this.someData); }
}
anotherObject.someAction();
