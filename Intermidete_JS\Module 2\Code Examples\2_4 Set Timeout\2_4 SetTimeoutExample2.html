<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Timeout</title>
</head>
<body>

    <h1>Using Set Timeout With CallBacks</h1>
    <p>This example mimics the loading of data. </p>
    <div id="progress">

    </div>

    <div>
        <button onclick="loadData()"> Load Data</button>

    </div>

    <script src="2_4 SetTimeoutExample2.js"></script>

    
</body>
</html>