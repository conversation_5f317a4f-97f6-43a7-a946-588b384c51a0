const { fizzBuzz, runFizzBuzz } = require('./fizzbuzz');

describe('FizzBuzz Tests', () => {
    
    describe('fizzBuzz function', () => {
        
        test('should return "Fizz" for numbers divisible by 3 only', () => {
            expect(fizzBuzz(3)).toBe("Fizz");
            expect(fizzBuzz(6)).toBe("Fizz");
            expect(fizzBuzz(9)).toBe("Fizz");
            expect(fizzBuzz(12)).toBe("Fizz");
            expect(fizzBuzz(21)).toBe("Fizz");
        });

        test('should return "Buzz" for numbers divisible by 5 only', () => {
            expect(fizzBuzz(5)).toBe("Buzz");
            expect(fizzBuzz(10)).toBe("Buzz");
            expect(fizzBuzz(20)).toBe("Buzz");
            expect(fizzBuzz(25)).toBe("Buzz");
            expect(fizzBuzz(35)).toBe("Buzz");
        });

        test('should return "FizzBuzz" for numbers divisible by both 3 and 5', () => {
            expect(fizzBuzz(15)).toBe("FizzBuzz");
            expect(fizzBuzz(30)).toBe("FizzBuzz");
            expect(fizzBuzz(45)).toBe("FizzBuzz");
            expect(fizzBuzz(60)).toBe("FizzBuzz");
            expect(fizzBuzz(75)).toBe("FizzBuzz");
            expect(fizzBuzz(90)).toBe("FizzBuzz");
        });

        test('should return the number as string for numbers not divisible by 3 or 5', () => {
            expect(fizzBuzz(1)).toBe("1");
            expect(fizzBuzz(2)).toBe("2");
            expect(fizzBuzz(4)).toBe("4");
            expect(fizzBuzz(7)).toBe("7");
            expect(fizzBuzz(8)).toBe("8");
            expect(fizzBuzz(11)).toBe("11");
            expect(fizzBuzz(13)).toBe("13");
            expect(fizzBuzz(14)).toBe("14");
        });

        test('should handle edge cases', () => {
            expect(fizzBuzz(0)).toBe("FizzBuzz"); // 0 is divisible by both 3 and 5
            expect(fizzBuzz(100)).toBe("Buzz");   // 100 is divisible by 5
            expect(fizzBuzz(99)).toBe("Fizz");    // 99 is divisible by 3
        });

        test('should handle negative numbers', () => {
            expect(fizzBuzz(-3)).toBe("Fizz");
            expect(fizzBuzz(-5)).toBe("Buzz");
            expect(fizzBuzz(-15)).toBe("FizzBuzz");
            expect(fizzBuzz(-1)).toBe("-1");
        });
    });

    describe('runFizzBuzz function', () => {
        let consoleSpy;

        beforeEach(() => {
            consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        });

        afterEach(() => {
            consoleSpy.mockRestore();
        });

        test('should call console.log 100 times', () => {
            runFizzBuzz();
            expect(consoleSpy).toHaveBeenCalledTimes(100);
        });

        test('should print correct sequence for first 15 numbers', () => {
            // Mock runFizzBuzz to only run first 15 numbers for testing
            for (let i = 1; i <= 15; i++) {
                console.log(fizzBuzz(i));
            }

            const expectedCalls = [
                ["1"], ["2"], ["Fizz"], ["4"], ["Buzz"],
                ["Fizz"], ["7"], ["8"], ["Fizz"], ["Buzz"],
                ["11"], ["Fizz"], ["13"], ["14"], ["FizzBuzz"]
            ];

            expectedCalls.forEach((call, index) => {
                expect(consoleSpy).toHaveBeenNthCalledWith(index + 1, call[0]);
            });
        });

        test('should include specific FizzBuzz values in the sequence', () => {
            runFizzBuzz();
            
            // Check that FizzBuzz appears for multiples of 15
            expect(consoleSpy).toHaveBeenCalledWith("FizzBuzz");
            
            // Check specific calls
            const allCalls = consoleSpy.mock.calls.map(call => call[0]);
            expect(allCalls[14]).toBe("FizzBuzz"); // 15th call (index 14) should be FizzBuzz
            expect(allCalls[29]).toBe("FizzBuzz"); // 30th call (index 29) should be FizzBuzz
        });
    });

    describe('Integration tests', () => {
        test('should produce correct output for classic FizzBuzz range 1-100', () => {
            const results = [];
            for (let i = 1; i <= 100; i++) {
                results.push(fizzBuzz(i));
            }

            // Test some key positions
            expect(results[2]).toBe("Fizz");    // position 3
            expect(results[4]).toBe("Buzz");    // position 5
            expect(results[14]).toBe("FizzBuzz"); // position 15
            expect(results[99]).toBe("Buzz");   // position 100

            // Count occurrences
            const fizzCount = results.filter(r => r === "Fizz").length;
            const buzzCount = results.filter(r => r === "Buzz").length;
            const fizzBuzzCount = results.filter(r => r === "FizzBuzz").length;

            expect(fizzBuzzCount).toBe(6); // 15, 30, 45, 60, 75, 90
            expect(fizzCount).toBe(27);    // Multiples of 3 but not 15
            expect(buzzCount).toBe(14);    // Multiples of 5 but not 15
        });
    });
});
