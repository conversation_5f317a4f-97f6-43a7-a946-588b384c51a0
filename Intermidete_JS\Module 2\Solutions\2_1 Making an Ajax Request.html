<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assignment Making An Ajax Request</title>

    <style>
        table {
            border: 5px solid black;
            border-collapse: collapse;
            visibility: visible;
            width: 50%;
        }

        tr:nth-child(even) {
            background-color: lightskyblue
        }

        tr:nth-child(odd) {
            background-color: lightgrey
        }

        th {
            background-color: white;
        }

        td {
            border: 1px solid black;
        }

        #btn1 {
            width: '75px';
            margin: 10px;
            padding: 1em;
            border: none;
            color:white;
            background-color: darkgreen;
        }
    </style>
</head>

<body>
    <h1>Making an Ajax Request</h1>
    <table id="table">
        <tr>
            <th>Product Name</th>
            <th>Serving</th>
            <th>Package </th>
            <th>Price</th>
            <th>Qty</th>
        </tr>
    </table>

    <input type='button' id="btn1" onclick="sendAjaxRequest()" value='Execute JS'>


    <script src="2_1 Solution Ajax.js"></script>
</body>

</html>