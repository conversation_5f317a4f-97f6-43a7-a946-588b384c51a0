<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regex Assignment</title>

    <style>
        div{
            margin: 15px auto;
            text-align: center;

        }
        label{
            display: inline-block;
            width: 250px;
            text-align: right;
        }
        form{
            margin: 25px auto;
            padding: 15px;
            max-width: 500px;
            border: 2px solid black;
            background-color: rgb(219, 219, 219);
        }
        h3{
            text-align: center;
        }

        .btn{
            background-color: rgb(48, 116, 48);
            color: white;
            padding: 10px;
            border: none;
        }

        .disable{
            background-color: rgb(116, 48, 48);
        }
        #errors{
            color:darkred;
        }
    </style>
</head>

<body>

    <h3>New Password Form</h3>
    <form action="Assignment 4_1 RegexConfirmation.html">
        <div>
            <label for="password">Enter Your New Password</label>
            <input type="password" name="password" id="password" onblur="validatePassword(this.value)">
        </div>
        <div>
            <label for="password-confirm">Confirm Your Password</label>
            <input type="password" name="password-confirm" id="password-confirm" onblur="validateConfirm(this.value)">
        </div>
        <div>
            <input class="btn" type="submit" value="Create Password" id="submit">
        </div>
        <div >
            <ul id="errors">

            </ul>
        </div>
    </form>

    <script src="Assignment 4_1 Regex.js"></script>
</body>

</html>