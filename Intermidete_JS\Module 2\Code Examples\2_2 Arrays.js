
//ARRAYS EXAMPLES

//create an array of 5 numbers.
var myArray = [1, 2, 3, 4, 5];
console.log(myArray.length); // 5
//the length is 5

console.log(myArray[2])
//index 0, 1, 2
//returns the 3rd item in the array
//returns the value 3

myArray.length--; //subtract one from the length of the array. 
console.log(myArray.length); // 4, the array is now smaller.

myArray.length + 15; //add 15 slots to the array
console.log(myArray.length); // 4, the array is now larger

console.log(myArray[50])
//we try to access index 50. nothing is there, so we get undefined returned
//in other languages, we would get a error, but not in JS

var animals = ['cats', 'dogs', 'elephants', 'lions', 'bears']
console.log(animals[4])
//arrays can contain text or any other datatype. it can mix data types

var mixed = ['cats', 'dogs', 102, 'lions', true, undefined]
console.log(mixed[3], mixed[4], mixed[5])

//we can take a value from an array and assign it to a new variable
var myLion = animals[3]
console.log(myLion)

//comparing the lions
if (myLion == animals[3]){
  console.log("two " + myLion + " in the jungle")
}

if (myLion != animals[0])
  {
    console.log(myLion + " are not the same as " + animals[0] )
  }

//the sort method, sort the array a-z ascending
animals.sort();
console.log(animals);

//reverse the order of the array
animals.reverse();
console.log(animals);

let anotherAnimal = "Zebra";
animals.push(anotherAnimal); //Add "zebra" as the last item to the array
console.log(animals);

let lastElement = animals.pop(); //Take the last element off the array. It's the Zebra
console.log(lastElement);

let firstElement = animals.shift() //Take the first element off the array. It's the lion.
console.log(animals);
console.log(firstElement);

//put the first element back, but at the end.
animals.push(firstElement);

//put the last element back, but at the beginning.
animals.unshift(lastElement);
console.log(animals)

//create a new array and fill it with default values.
let largeArray = new Array(50); //new array, 50 elements, all elements are undefined.
largeArray.fill("Some Data", 0, 50); //fill the array from 0 to 50 with "Some Data";
console.log(largeArray.length);
console.log(largeArray);

//Create a string from an array.
let myfavoriteanimals = animals.join(',') // each will be seperated by a comma
console.log ("My favorite animals are " + myfavoriteanimals);

//Create a new array from two small ones.
let firstArray = ["Batman","Superman"];
let secondArray = ["Batwoman","Wonder Woman"];
let superHeros = firstArray.concat(secondArray);
console.log(superHeros);

superHeros.splice(2,0, "Flash"); //insert the flash at index 2. Remove 0 other elements that came after.
console.log(superHeros);
superHeros.splice(3,1, "Ironman"); //insert the Ironman at index 3. Remove the 1 element that came after.
console.log(superHeros);
superHeros.splice(1,4, "Hulk"); //insert the Hulk at index 1. Remove the 4 element that came after there.
console.log(superHeros);

//DOM Output

let ul = document.createElement("ul");
ul.innerText = "My Favorite SuperHeros are"
document.body.append(ul);
for(hero of superHeros){
  let li = document.createElement("li");
  li.innerText = hero;
  ul.append(li);
}

let ul2 = document.createElement("ul");
ul2.innerText = "My Favorite animals are"
document.body.append(ul2);
for(animal of animals){
  let li = document.createElement("li");
  li.innerText = animal;
  ul2.append(li);
}

let flexRow = document.getElementById("flex-row");
for(item of largeArray){
  let flexRowItem = document.createElement("div");
  flexRowItem.innerText = item;
  flexRow.append(flexRowItem);
}
