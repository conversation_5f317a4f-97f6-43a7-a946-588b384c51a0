"use strict";

//Custom Error Handling Example 2

//We've created a function that will throw an custom error when an ammount is less than zero.
function amountErrorCheck(amount){
    if (amount <= 0.00){
        throw "CustomError: Amount can not be a negative number."
    }
  }
  
  var amount = -50.00
  
  try{
    amountErrorCheck(amount)
  }
  catch (e){
    amount = 0.00
    console.log("Handled error. The error was: ")
    console.error(e);
  }
  finally{
    //Regardless of if an error was thrown, we will print the amount to the console.
    console.log(amount);
  }

  //We reach the end of the script, even if the error was thrown.
  console.log("End of script.")
