'use strict';
const storiesContainer = document.getElementById('stories-container');

function loadNewStories(guardian) {
    // Access fetched data as array
    let allStories = Object.entries(guardian.response.results);

    // Create each article using map
    allStories.map((story) => {

        console.log(story);
        // Construct content of each with template literal              
        let articleContent = `
                <h3>${story[1].webTitle}</h3>
                <img src="${story[1].fields.thumbnail}">
                <p>${story[1].fields.trailText}</p>
                `

        // Create article element (container)
        let article = document.createElement('article');
        // Set innerHTML of article element to template
        article.innerHTML = articleContent;
        // Create and append button to show full story
        let link = document.createElement("a")
        link.href = story[1].webUrl;
        //let button = document.createElement('button');
        link.innerText = "Read full story";
        link.classList.add("showStory");
        //button.addEventListener('click', showFullStory);
        article.append(link);
        storiesContainer.append(article);
    });
}

// Generate button to refresh headlines and
// append to header element
let button = document.createElement('button');
button.id = "refresh-btn";
button.innerText = "Refresh headlines";
button.addEventListener('click', () => {
    //location.reload() means reload current page.
    location.reload();
});
header.append(button);

fetch('https://content.guardianapis.com/search?section=world&show-fields=all&show-blocks=body&api-key=test')
.then(res => res.json())
.then(res => loadNewStories(res))
