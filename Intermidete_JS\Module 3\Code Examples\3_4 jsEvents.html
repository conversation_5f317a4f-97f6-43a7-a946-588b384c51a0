<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="html, training, lessons, templates">
  <meta lang="en-US">

  <title>JS Events</title>
  <style>
    .background {
      box-sizing: border-box;
      width: 500px;
      height: 500px;
      background-color: rgb(200, 200, 200);
      margin: auto;
      border: 2px solid black;
      font-size: 2em;
      display: flex;
      align-items: center;
      justify-content: center;

    }


    #btn1 {
      box-sizing: border-box;
      width: 50%;
      height: 50%;
      margin: auto;
      float: right;
    }

    .flex-container {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }
  </style>
</head>


<body>
  <h1>JS Events - Events written into HTML</h1>
  <div id='target' class="background">
    <input type='button' onmouseover="jsMouseOver()" onmouseout="jsMouseOut()" onclick="countClicks()" id="btn1"
      value='Hoover Over and Click Me'>
  </div>

  <h2>Number of Button Clicks</h2>
  <div id="clicks" class="flex-container">

  </div>


  <script>
    let totalClicks = 1;

    function jsMouseOver() {
      var element = document.getElementById("btn1");
      element.style.backgroundColor = '#20c8df';
    }

    function jsMouseOut() {
      var element = document.getElementById("btn1");
      element.style.backgroundColor = '#f0eb5d';
    }

    function countClicks() {
      var newElement = document.createElement("div");
      newElement.style.background = "blue";
      newElement.style.color = "white";
      newElement.style.margin = "10px";
      newElement.style.padding = "5px";
      newElement.innerText = totalClicks;
      let parent = document.getElementById("clicks");
      parent.append(newElement);
      totalClicks++;
    }
  </script>

</body>

</html>