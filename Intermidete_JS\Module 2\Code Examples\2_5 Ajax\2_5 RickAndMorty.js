function ajax(callback){
    var requestURL = 'https://rickandmortyapi.com/api/character/1';
 
   
    var request = new XMLHttpRequest;
    request.open('GET', requestURL);
    
    request.onreadystatechange = function(){
      console.log(this.readyState + " " + this.status + " " + this.statusText);
      if (this.readyState == 4 && this.status == 200){
        var result = this.responseText;
        callback(result)
      }
    }
  
    request.send();
  
}

function displayCharacter(result){
  console.log(result);
  let data = JSON.parse(result);
  let name = document.createElement("h2");
  let orgin = document.createElement("h2");
  orgin.innerText = data.origin.name;
  name.innerText = data.name;
  let img = document.createElement("img")
  img.src = data.image;
  document.body.append(name);
  document.body.append(orgin);
  document.body.append(img);
}
  
  
ajax(displayCharacter)