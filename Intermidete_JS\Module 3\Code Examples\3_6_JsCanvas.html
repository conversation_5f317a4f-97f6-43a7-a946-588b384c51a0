<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JS Canvas</title>

    <style>
      body{
        margin: 0;
      }
    </style>

  </head>
  <body>
    <body>
      
      <canvas id="canvas" onmousedown="BeginDraw(event)" onmouseup="EndDraw()" onmousemove="OnDraw(event)">
        Sorry, If you can read this text, your browser does not support HTML Canvas.
      </canvas>

      <script src="3_6_jsCanvas.js"></script>
    </body>
  </body>
</html>