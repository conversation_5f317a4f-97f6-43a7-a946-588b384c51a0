<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bangers&display=swap" rel="stylesheet">
    <title>Set Timeout</title>

    <style>
        #display{
            font-size: 6em;
            color: red;
            font-family: 'Bangers', cursive;;

        }

    </style>

</head>
<body>

    <h1>Using Set Timeout - Simple Example </h1>
    <p>This example shows that we can set a timeout and for a wait.</p>

    <div>
        <label for="waittime">How many seconds would you like to wait?</label>
        <input id="waittime" type="number" min="1", max="10">
    </div>

    <div>
        <button onclick="waitForIt()"> Wait For It </button>
    </div>

    <div id="display">

    </div>

    <script src="2_4 SetTimeoutExample.js"></script>

    
</body>
</html>