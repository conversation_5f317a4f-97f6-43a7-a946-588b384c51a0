'use strict';
//A sample function for making an AJAX call.
function ajaxExample() {

  //The URL to contact
  var requestURL = "https://httpbin.org/get";

  //Create a new request object
  var request = new XMLHttpRequest;

  //Set what will occur when we get the request back.
  //onreadystatechange is equal to a function that describes what will be done with the results.

  request.onreadystatechange = function () {
    //Log the status of the request.
    console.log(this.readyState + " " + this.status + " " + this.statusText);

    //If the status is 200 (OK) and the readyState is 4 (Complete),
    //Log the response to the console.
    if (this.readyState == 4 && this.status == 200) {
      var result = this.responseText;
      console.log(result);

      let pre = document.createElement("pre");
      pre.innerText = result;
      document.body.append(pre);

    }
  }

  //Open the connecton to the Web Service Address/Endpoint
  console.log("Openning the request...")
  request.open('GET', requestURL);
  //Fire the Request
  console.log("Sending the request...")
  request.send();

}


ajaxExample()