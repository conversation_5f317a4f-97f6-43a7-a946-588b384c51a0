<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fizz Buzz</title>
    <style>
        div{
            margin: 15px auto;
            text-align: center;

        }
        label{
            display: inline-block;
            width: 250px;
            text-align: right;
        }
        form{
            margin: 25px auto;
            padding: 15px;
            max-width: 500px;
            border: 2px solid black;
            background-color: rgb(219, 219, 219);
        }
        h3{
            text-align: center;
        }

        .btn{
            background-color: rgb(48, 116, 48);
            color: white;
            padding: 10px;
            border: none;
        }

        /* color of the button when disabled. */
        .disable{
            background-color: rgb(116, 48, 48);
        }
        /* color of error text on the page */
        #errors{
            color:darkred;
        }
    </style>
</head>
<body>
    <h1>FizzBuzz Assignment</h1>
    <script src= "Assignment 4_2 Fizzbuzz.js"></script>
</body>
</html>