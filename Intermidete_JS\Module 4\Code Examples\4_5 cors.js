const url = 'http://127.0.0.1:5500/4_5 CORS-Other.html'
fetch(url).then(response => response.text()).then(html => document.getElementById("content").innerHTML = html);


const source = 'http://127.0.0.1:5500/4_5 cors-otherscript.js';

let script = document.createElement("script");
script.src = source;
document.getElementById("content").append(script)

const crossContent = "http://localhost:5000/htmlpage.html"
fetch(crossContent).then(response => response.text()).then(html => document.getElementById("content").innerHTML = html);

