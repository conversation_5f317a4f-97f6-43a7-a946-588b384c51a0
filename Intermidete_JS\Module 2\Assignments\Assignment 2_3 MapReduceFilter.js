"use strict";

let ID = 0;


function Zombie(health, type){
    this.health = health;
    this.type = type;
    this.id = "z" + ID;
    ID++;
}

function generateZombie(group, parent){
    for(let zombie of group){
        let element = document.createElement("div");
        element.id = zombie.id;
        let img = document.createElement("img");
        let p = document.createElement("p");
        img.src = zombie.type === "normal" ? 'zombie.png' : 'special zombie.png';
        p.innerText = `HP: ${zombie.health}`;
        p.classList.add("healthbar");
        parent.append(element);
        element.append(img);
        element.append(p);
    }
}

function weakenZombies(){
     let newHealth = zombieGroupA.map( z => z.health * .5)
    for(let i = 0 ; i < newHealth.length; i++)
    {
        zombieGroupA[i].health = newHealth[i];
        document.getElementById(zombieGroupA[i].id).children[1].innerText = `HP: ${zombieGroupA[i].health}`;
    }

}

function calculateAmmo(){
    let ammo = (zombieGroupB.reduce( (health, z) => {return health + z.health}, 0 ) );
    let output = document.getElementById("ammo");
    output.innerText = ammo;
}

function findSpecial(){
    let special = zombieGroupC.filter( (z) => { return z.type === "special"});
    let html = document.getElementById("group-c");
    for (let element of html.children){
        if(element.id != special[0].id && element.id != "exercise-3")
        {
            element.remove();
        }
    }

}


let zombieGroupA = [
    new Zombie(200, "normal"),
    new Zombie(300, "normal"),
    new Zombie(250, "normal"),
]

let zombieGroupB = [
    new Zombie(80, "normal"),
    new Zombie(50, "normal"),
    new Zombie(125, "normal"),
    new Zombie(85, "normal"),

]

let zombieGroupC = [
    new Zombie(100, "normal"),
    new Zombie(100, "special"),
    new Zombie(100, "normal"),
]

generateZombie(zombieGroupA, document.getElementById("group-a") );
generateZombie(zombieGroupB, document.getElementById("group-b") );
generateZombie(zombieGroupC, document.getElementById("group-c") );

