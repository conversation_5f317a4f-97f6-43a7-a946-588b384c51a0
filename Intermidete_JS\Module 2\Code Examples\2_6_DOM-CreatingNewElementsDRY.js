"use strict";
/*This is an example of DRY - Don't repeat yourself.  We would be writing 
a lot of lines code over and over such as let element = document.CreateElement

To reduce the amount of code, we've created custom, reuseable functions for the 
tasks of creating elements, adding classes, and setting content.
*/

//Create and new Element with the document.CreateElement
//and set the id attribute with .setAttribute
//Then place the new element on the document body.
function CreateElement(html, id) {
  let element = document.createElement(html);
  element.setAttribute("id", id)
  document.body.append(element);
}


//Add an additional classname to the list of classes
//using .classList.Add()
function AddClass(id, className) {
  let element = document.getElementById(id);
  element.classList.add(className);
}

//Set some Content for an Element
function SetContent(id, content){
  let element = document.getElementById(id);
  element.innerHTML = content;
}

//Create a new Element with the document.CreateElement
//and set the id attribute with .setAttribute
//Then place the new element as a child of the parent elememt.
function CreateChildElement(html, id, parentElement) {
  let element = document.createElement(html);
  element.setAttribute("id", id)
  parentElement.append(element);
}

//Create some explantory text
CreateElement("p", "info");
AddClass("info", "content");
AddClass("info", "text-italic");
SetContent("info", "DRY stands for Don't Repeat Yourself.  DRY means avoiding re-writing similar code over and over.")


//Create a button with id of id-1
CreateElement("button", "id-1");
AddClass("id-1", "content");
SetContent("id-1", "Functions help us write DRY. We can write complex code once, then reuse it as a function.");

//Create a div with id of id-2
CreateElement("div", "id-2");
AddClass("id-2", "content");
SetContent("id-2", "DRY code is easier to update and maintain, and reusable in other projects.");


//Create a div with id of flex-parent
CreateElement("div", "flex-parent");
AddClass("flex-parent", "flex-row");
AddClass("flex-parent", "align-center");

//Let's store the flexbox parent as a variable. We will need it later.
let flexParent = document.getElementById("flex-parent")

//Use a loop to create children in the flexbox.
let flexItems = [1,2,3,4,5,6,7,8]
for (let item of flexItems)
{
  CreateChildElement("div", "flexitem-"+item, flexParent);
  AddClass("flexitem-"+item, "flex-item");
  SetContent("flexitem-"+item, item);
}
