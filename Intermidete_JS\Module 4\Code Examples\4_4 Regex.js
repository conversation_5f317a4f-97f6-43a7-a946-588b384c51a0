'use strict';
//Forms property

let sampleText = "Yesterday, I ate a green apple and 2 slices of bread for breakfast. It was delicious!  Then, I took my dog for a walk. He was very happy to spend time outside. "
let myRegex = new RegExp("[0-9]")

if (sampleText.match(myRegex) )
{
    console.log("Match!")
}
else{
    console.log("No Match!")
}


let text1 = "THe"
let doubleCaps = new RegExp("[A-Z][A-Z]")

if (text1.match(doubleCaps) )
{
    console.log("Match! Two capital letters in a row detected")
}
else{
    console.log("No Match!")
}

let emailAddress = "<EMAIL>"
let emailRegex = new RegExp("[@]\\w+[.][a-z]{2,5}");
if (emailAddress.match(emailRegex) )
{
    console.log("Match! Appears to be a valid email")
}
else{
    console.log("No Match!")
}



//We can access forms using the forms property of the document object.
//This can be useful for quickly working with form data.

//Prefilled form data.
// document.forms[0].firstName.value = "Test";
// document.forms[0].lastName.value = "User";
// document.forms[0].email.value = "<EMAIL>";
// document.forms[0].birthday.value = "1974-05-19"

function validate(){

    let containsNumeric = new RegExp("[0-9]")
    let firstName = document.forms[0].firstName.value;
    let lastName = document.forms[0].lastName.value;
    if(firstName.match(containsNumeric) || lastName.match(containsNumeric))
    {
        validationError("Name can not contain numbers.")
    }
    else 
    {
        clearValidationError();
    }
}

function validationError(msg){
    let error = document.getElementById("error");
    error.innerText = "Validation Error: " + msg;
    error.style.color = "red";
}

function clearValidationError(){
    let error = document.getElementById("error");
    error.innerText = "";
}

