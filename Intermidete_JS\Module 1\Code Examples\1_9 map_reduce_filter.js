'use strict'
//MAP, REDUCE, and FILTER ARRAY METHODS

//An array with values 1,2,3,4,5,6,7
let myNumbers = [1,2,3,4,5,6,7];  //a simple array.

//An array with more numbers.
let moreNumbers = [2,3,4,5,35];

//An Array with words.
let words = ['man','woman','cat','dog','nintendo'];


//MAP EXAMPLES
//Create a new array by doubling each number in the original array.
let doubleArray = myNumbers.map( number => number * 2);
console.log(doubleArray);
//[2, 4, 6, 8, 10, 12, 14]

//Create a new array by tripling each number in the original array.
let tripleArray = myNumbers.map( (n) => { return n * 3} );
console.log(tripleArray);
//[3, 6, 9, 12, 15, 18, 21]


//Create a new array by halfing each number in the original array.
let halfArray = myNumbers.map(x => x / 2);
console.log(halfArray);
/*
[ 0.5, 1, 1.5, 2, 2.5, 3, 3.5]
*/

let superWords  = words.map(word => 'super ' + word);
console.log(superWords);
/*
[
  'super man',
  'super woman',
  'super cat',
  'super dog',
  'super nintendo'
]
*/

let timesTenArary = moreNumbers.map( item => { return item * 10});
console.log(timesTenArary)
//[ 20, 30, 40, 50, 350 ]

//REDUCE EXAMPLES
//reduce an array to a single value; 28
//1 + 2 + 3 + 4 + 5 + 6 + 7 = 28
let sumOfMyNumbers = myNumbers.reduce(  (previousNumber, currentNumber) => previousNumber + currentNumber );
console.log(sumOfMyNumbers);


//reduce an array to a single value; 49
let sumOfMoreNumbers = moreNumbers.reduce( (prev, curr) =>  {return prev + curr});
console.log(sumOfMoreNumbers);

//Find the average
//reduce moreNumbers to it's sum, then divide by it's length. 9.8
let avg = (moreNumbers.reduce( (prev, curr) =>  {return prev + curr})) / moreNumbers.length;
console.log(avg);

//FILTER EXAMPLES
//filter elements of an array

//Only return numbers from the orginial array that can be evenly divided by 2 (even numbers)
//returns 2, 4, 6 
let evenNumbers = myNumbers.filter( number => number % 2 == 0) ;
console.log(evenNumbers);


//Only return numbers from the orginial array that can NOT be evenly divided by 2 (odd numbers)
//returns 1, 3, 5, 7 
let oddNumbers = myNumbers.filter( (number) =>  {return number % 2 != 0}) ;
console.log(oddNumbers);

//returns just 35
let moreThanFive = moreNumbers.filter ( (number) => number > 5 )
console.log(moreThanFive);


//The clunky, older forEach method
//declarea  variable for storing the results.
let x = 0;
//for each number in array, increase x.
moreNumbers.forEach(  (n)=> {x += n;} )
console.log(x);



