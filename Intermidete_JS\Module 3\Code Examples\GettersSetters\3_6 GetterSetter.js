'use strict';

function Diner(){
    //public properties
    this.menu = {breakfast:'Eggs', lunch:'burgers', dinner:'Pasta'};

    //private properties
    let kitchen = "Closed";

    //private methods

    

    //public methods
    this.order = function(order) {
        for(let key in this.menu){
            if(order === key){
               return  prepareOrder(this.menu[key]);

            }
            else{
                return `${order} is not available on the menu today`;
            }
        }
    }

    this.open = function(){
        if(kitchen != "Open"){
            kitchen = "Open";
        }
    }

    this.close = function(){
        if(kitchen != "Closed"){
            kitchen = "Closed";
        }
    }

    //private methods
    function prepareOrder(order){
        if (kitchen === "Open"){
            return `Made an order of ${order}`;
        }
        else{
           return `The kitchen is not open`;
        }
    }

}


