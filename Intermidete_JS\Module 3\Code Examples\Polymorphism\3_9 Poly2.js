'use strict';
//PolyMorpism Example 2
//Create A Parent
function Animal(name){
    this.name = name;

}
//Create Parent Methods on the prototype chain
Animal.prototype.breathe = function() {
    console.log(`The ${this.name} breaths through it's nose.`);
}
Animal.prototype.eat = function() {
    console.log(`The ${this.name} eats with it's mouth`);
}

//Create a Child, inhert parent properties
function Fish(name){
    Animal.call(this, name);
    this.gills = true;
}
//Inhert a new copy of the prototype chain
Fish.prototype = Object.create(Animal.prototype);


//Override the breath method on the chain.
Fish.prototype.breathe = function() {
    console.log(`The ${this.name}  breathes through it's gills.`);
}

//The animal will breathe like normal.
let rabbit = new Animal("rabbit");
console.log(rabbit);
rabbit.breathe();


//The fish will breathe with different way from the Parent.
let fish = new Fish("fish");
console.log(fish)
fish.breathe();
console.log (fish instanceof Animal);
console.log (fish instanceof Fish);
