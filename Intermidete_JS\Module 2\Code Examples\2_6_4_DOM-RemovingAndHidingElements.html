<!DOCTYPE html>
<html>
  <head>

    <style>

      .content{
        background-color: #ccc;
        border: 2px solid black;
        padding: 10px;
        width: 50%;
        margin: 20px;
      }

      .flex-row{
        display: flex;
      }

      .hide{
        display: none;
      }


    </style>

  </head>
  <body>

    <div class=content>
      <p>
        When prompted, type the id of the element to remove. The options are
        <ul>
          <li>one - the first flexbox, and all it's children</li>
          <li>two - content with id two</li>
          <li>three - content with id three</li>
          <li>four - content with id four</li>
          <li>five - the second flexbox, and all it's children</li>
          <li>six - content with id six</li>
          <li>seven - content with id seven</li>
          <li>eight - content with id eight</li>
        </ul>
      </p>
    </div>

    <div class="flex-row" id="one">
      <div class="content" id="two"> Content ID two</div>
      <div class="content" id="three">Content ID three </div>
      <div class="content" id="four">Content ID four </div>
    </div>

    <div class="flex-row" id="five">
      <div class="content" id="six"> Content ID six</div>
      <div class="content" id="seven">Content ID seven </div>
      <div class="content" id="eight">Content ID eight </div>
    </div>


    <script src="2_6_DOM_Removing.js"></script>
  </body>
</html>