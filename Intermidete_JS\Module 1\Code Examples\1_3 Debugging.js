'use strict'
let fn = 1;


function changeState()
{

    let nextNum = fibbanacci(fn)
    displayfibbanacci(nextNum, fn);
    fn++;


}

function fibbanacci(n)
{
    if(n <= 1){
        return  n;
    }


    return fibbanacci(n-1) + fibba<PERSON><PERSON>(n-2);

}

function displayfibbanacci(nextNum, fn){
    
    let next = document.createElement("div");
    let sequence = document.createElement("div");
    next.className = "number";
    next.textContent = nextNum;
    sequence.textContent = fn;
    sequence.className = "sequence"

    document.getElementById("next").append(next);

    document.getElementById("fn").append(sequence);
}

