'use strict';
//PolyMorpism Example
//Create A Parent
function Parent(name){
    this.name = name;

}
//Create Parent Methods on the prototype chain
Parent.prototype.doWork = function() {
    console.log("Do Some Work");
}

//Create a Child, inhert parent properties
function Child(name){
    Parent.call(this, name);
    this.doWork = function(){
        Parent.prototype.doWork();
        console.log("Do Some More Work As Child");
    }

}
//Inhert a new copy of the prototype chain
Child.prototype = Object.call(Parent.prototype);
//Override the doWork method on the chain.

//The parent will doWork like normal.
let parent = new Parent("parent");
console.log(parent);
parent.doWork();


//The child will doWork with different outcome fro the Parent.
let child = new Child("child");
console.log(child)
child.doWork();
