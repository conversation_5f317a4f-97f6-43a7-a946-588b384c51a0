const {
    findDuplicatesRecursive,
    findDuplicatesRecursiveSlice,
    findDuplicatesWithCounts,
    findFirstDuplicate,
    hasDuplicates,
    demonstrateFunctions
} = require('./findDuplicates');

describe('Find Duplicates Functions', () => {
    
    describe('findDuplicatesRecursive', () => {
        test('should find duplicates in array with numbers', () => {
            const result = findDuplicatesRecursive([1, 2, 3, 2, 4, 5, 3, 6, 1]);
            expect(result.sort()).toEqual([1, 2, 3]);
        });

        test('should return empty array when no duplicates', () => {
            const result = findDuplicatesRecursive([1, 2, 3, 4, 5]);
            expect(result).toEqual([]);
        });

        test('should handle empty array', () => {
            const result = findDuplicatesRecursive([]);
            expect(result).toEqual([]);
        });

        test('should handle single element array', () => {
            const result = findDuplicatesRecursive([1]);
            expect(result).toEqual([]);
        });

        test('should handle array with all same elements', () => {
            const result = findDuplicatesRecursive([5, 5, 5, 5]);
            expect(result).toEqual([5]);
        });

        test('should handle strings', () => {
            const result = findDuplicatesRecursive(['a', 'b', 'c', 'a', 'd', 'b']);
            expect(result.sort()).toEqual(['a', 'b']);
        });

        test('should handle mixed data types', () => {
            const result = findDuplicatesRecursive([1, '1', 2, 1, '2', '1']);
            expect(result.sort()).toEqual(['1', 1]);
        });
    });

    describe('findDuplicatesRecursiveSlice', () => {
        test('should find duplicates using slice method', () => {
            const result = findDuplicatesRecursiveSlice([1, 2, 3, 2, 4, 5, 3, 6, 1]);
            expect(result.sort()).toEqual([1, 2, 3]);
        });

        test('should return empty array when no duplicates', () => {
            const result = findDuplicatesRecursiveSlice([1, 2, 3, 4, 5]);
            expect(result).toEqual([]);
        });

        test('should handle empty array', () => {
            const result = findDuplicatesRecursiveSlice([]);
            expect(result).toEqual([]);
        });

        test('should handle large arrays', () => {
            const largeArray = Array.from({length: 1000}, (_, i) => i % 100);
            const result = findDuplicatesRecursiveSlice(largeArray);
            expect(result.length).toBe(100); // 0-99 all appear multiple times
        });
    });

    describe('findDuplicatesWithCounts', () => {
        test('should find duplicates and return as strings', () => {
            const result = findDuplicatesWithCounts([1, 2, 3, 2, 4, 5, 3, 6, 1]);
            expect(result.sort()).toEqual(['1', '2', '3']);
        });

        test('should return empty array when no duplicates', () => {
            const result = findDuplicatesWithCounts([1, 2, 3, 4, 5]);
            expect(result).toEqual([]);
        });

        test('should handle multiple occurrences', () => {
            const result = findDuplicatesWithCounts([1, 1, 1, 2, 2, 3]);
            expect(result.sort()).toEqual(['1', '2']);
        });
    });

    describe('findFirstDuplicate', () => {
        test('should find the first duplicate in order of appearance', () => {
            const result = findFirstDuplicate([1, 2, 3, 2, 4, 5, 3, 6, 1]);
            expect(result).toBe(2); // 2 appears again before 3 or 1
        });

        test('should return null when no duplicates', () => {
            const result = findFirstDuplicate([1, 2, 3, 4, 5]);
            expect(result).toBeNull();
        });

        test('should return null for empty array', () => {
            const result = findFirstDuplicate([]);
            expect(result).toBeNull();
        });

        test('should find first duplicate with strings', () => {
            const result = findFirstDuplicate(['a', 'b', 'c', 'b', 'a']);
            expect(result).toBe('b');
        });

        test('should handle immediate duplicate', () => {
            const result = findFirstDuplicate([1, 1, 2, 3]);
            expect(result).toBe(1);
        });
    });

    describe('hasDuplicates', () => {
        test('should return true when duplicates exist', () => {
            const result = hasDuplicates([1, 2, 3, 2, 4]);
            expect(result).toBe(true);
        });

        test('should return false when no duplicates', () => {
            const result = hasDuplicates([1, 2, 3, 4, 5]);
            expect(result).toBe(false);
        });

        test('should return false for empty array', () => {
            const result = hasDuplicates([]);
            expect(result).toBe(false);
        });

        test('should return false for single element', () => {
            const result = hasDuplicates([1]);
            expect(result).toBe(false);
        });

        test('should return true for immediate duplicates', () => {
            const result = hasDuplicates([1, 1]);
            expect(result).toBe(true);
        });
    });

    describe('Edge Cases and Performance', () => {
        test('should handle arrays with null and undefined', () => {
            const result = findDuplicatesRecursive([null, undefined, null, 1, undefined]);
            expect(result.sort()).toEqual([null, undefined]);
        });

        test('should handle arrays with objects (by reference)', () => {
            const obj1 = {a: 1};
            const obj2 = {a: 1};
            const result = findDuplicatesRecursive([obj1, obj2, obj1]);
            expect(result).toEqual([obj1]);
        });

        test('should handle boolean values', () => {
            const result = findDuplicatesRecursive([true, false, true, false, true]);
            expect(result.sort()).toEqual([false, true]);
        });

        test('all methods should produce consistent results', () => {
            const testArray = [1, 2, 3, 2, 4, 5, 3, 6, 1];
            
            const result1 = findDuplicatesRecursive(testArray).sort();
            const result2 = findDuplicatesRecursiveSlice(testArray).sort();
            const result3 = findDuplicatesWithCounts(testArray).map(Number).sort();
            
            expect(result1).toEqual([1, 2, 3]);
            expect(result2).toEqual([1, 2, 3]);
            expect(result3).toEqual([1, 2, 3]);
        });
    });

    describe('demonstrateFunctions', () => {
        let consoleSpy;

        beforeEach(() => {
            consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        });

        afterEach(() => {
            consoleSpy.mockRestore();
        });

        test('should call console.log multiple times', () => {
            demonstrateFunctions();
            expect(consoleSpy).toHaveBeenCalledTimes(6);
        });

        test('should log the original array', () => {
            demonstrateFunctions();
            expect(consoleSpy).toHaveBeenCalledWith("Original array:", [1, 2, 3, 2, 4, 5, 3, 6, 1]);
        });
    });
});
