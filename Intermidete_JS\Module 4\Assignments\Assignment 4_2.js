"use strict";

// Global variables to store current RGB values
let currentRed = 100;
let currentGreen = 100;
let currentBlue = 100;

/**
 * Updates the color value based on slider inputs
 * This function is called whenever any slider changes
 */
function updateColorValue() {
    // Get current values from sliders
    const redSlider = document.getElementById('red-control');
    const greenSlider = document.getElementById('green-control');
    const blueSlider = document.getElementById('blue-control');

    // Update global variables
    currentRed = parseInt(redSlider.value) || 0;
    currentGreen = parseInt(greenSlider.value) || 0;
    currentBlue = parseInt(blueSlider.value) || 0;

    // Update the color display
    updateColorDisplay();

    // Update the color code display
    updateColorCode();
}

/**
 * Updates the background color of the color box
 */
function updateColorDisplay() {
    const colorBox = document.getElementById('target');
    const rgbColor = `rgb(${currentRed}, ${currentGreen}, ${currentBlue})`;

    if (colorBox) {
        colorBox.style.backgroundColor = rgbColor;
    }
}

/**
 * Updates the color code display showing RGB and Hex values
 */
function updateColorCode() {
    const colorCodeElement = document.getElementById('color-code');

    if (colorCodeElement) {
        const rgbString = `rgb(${currentRed}, ${currentGreen}, ${currentBlue})`;
        const hexString = rgbToHex(currentRed, currentGreen, currentBlue);

        colorCodeElement.innerHTML = `
            <strong>RGB:</strong> ${rgbString}<br>
            <strong>HEX:</strong> ${hexString}
        `;
    }
}

/**
 * Converts RGB values to hexadecimal color code
 * @param {number} r - Red value (0-255)
 * @param {number} g - Green value (0-255)
 * @param {number} b - Blue value (0-255)
 * @returns {string} Hexadecimal color code
 */
function rgbToHex(r, g, b) {
    // Convert each component to hex and pad with zero if needed
    const redHex = r.toString(16).padStart(2, '0');
    const greenHex = g.toString(16).padStart(2, '0');
    const blueHex = b.toString(16).padStart(2, '0');

    return `#${redHex}${greenHex}${blueHex}`.toUpperCase();
}

/**
 * Sets RGB values programmatically
 * @param {number} r - Red value (0-255)
 * @param {number} g - Green value (0-255)
 * @param {number} b - Blue value (0-255)
 */
function setRGBValues(r, g, b) {
    // Clamp values between 0 and 255
    r = Math.max(0, Math.min(255, r));
    g = Math.max(0, Math.min(255, g));
    b = Math.max(0, Math.min(255, b));

    // Update sliders
    const redSlider = document.getElementById('red-control');
    const greenSlider = document.getElementById('green-control');
    const blueSlider = document.getElementById('blue-control');

    if (redSlider) redSlider.value = r;
    if (greenSlider) greenSlider.value = g;
    if (blueSlider) blueSlider.value = b;

    // Update global variables
    currentRed = r;
    currentGreen = g;
    currentBlue = b;

    // Update displays
    updateColorDisplay();
    updateColorCode();
}

/**
 * Generates a random color
 */
function randomColor() {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);

    setRGBValues(r, g, b);
}

/**
 * Resets color to default (gray)
 */
function resetColor() {
    setRGBValues(100, 100, 100);
}

/**
 * Gets the current color as an object
 * @returns {Object} Object containing RGB and hex values
 */
function getCurrentColor() {
    return {
        rgb: {
            r: currentRed,
            g: currentGreen,
            b: currentBlue
        },
        rgbString: `rgb(${currentRed}, ${currentGreen}, ${currentBlue})`,
        hex: rgbToHex(currentRed, currentGreen, currentBlue)
    };
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set initial slider values
    const redSlider = document.getElementById('red-control');
    const greenSlider = document.getElementById('green-control');
    const blueSlider = document.getElementById('blue-control');

    if (redSlider) redSlider.value = currentRed;
    if (greenSlider) greenSlider.value = currentGreen;
    if (blueSlider) blueSlider.value = currentBlue;

    // Initial color update
    updateColorDisplay();
    updateColorCode();

    console.log('Color Tool App initialized!');
});