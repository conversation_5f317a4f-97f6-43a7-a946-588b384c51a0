//Recursive Function
//It calls itself, but provides for a way to end the function.

function recursive(n)
{
    //If the input was not a number, throw an error.
    if(isNaN(n) || n < 1  )
    {
        try {
            throw "Must be a number or greater than one."
        }
        catch{
            n = 5
        }
    }

    console.log(`The value of n is ${n}`);
    //This is our exit condition.  We give it a "case" where we exit the function without calling itself again.
    if (n === 1){
        return n 
    }

    //This makes the function recursive. We call it again, but this time with the value of n - 1.
    recursive(n-1);
}

//Call the function with 10.
recursive(10);
